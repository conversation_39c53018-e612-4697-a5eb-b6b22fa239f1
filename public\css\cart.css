.cart{
    font-size: var(--10px);
}

.cart__wrapper {
    gap: 3em;
    display: flex;
    padding: 8em 0 6em;
    justify-content: center;
    min-height: 22.5em;
}

.cart__content {
    width: 70%;
}

.cart__notification-sold {
    margin-bottom: 2em;
    padding: 1em 2em;
    border-radius: 0.8em;
    display: flex;
    align-items: center;
    gap: 1.2em;
    background-color: rgb(247, 247, 247);
}

.cart__notification-sold > .icon-fire {
    width: 4.8em;
    height: 4.8em;    
    animation: tf-ani-flash 500ms infinite; 
}

@keyframes tf-ani-flash {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}

.notification-sold__count{
    font-size: 1.6em;
}

.notification-sold__time {
    display: inline-block;
    min-width: 5ch;
    text-align: center;
    color: red;
    font-weight: bold;
}

.products-cart {
  display: grid;
  gap: 1em;
  margin: auto;
  padding: 1em;
  box-sizing: border-box;
}

.cart-header {
  display: grid;
  grid-template-columns: 3fr 1fr 2fr 1fr 1fr;
  font-weight: bold;
  padding-bottom: 2em;
  border-bottom: 0.1em solid rgba(0, 0, 0, 0.4);  
  align-items: center;
}

.cart-header__title{
    font-size: 1.6em;
}

.product__item{
    display: grid;
    grid-template-columns: 1fr 5fr 2fr;
    gap: 0
}

.cart__item__img{
    font-size: var(--10px);
    border-radius: 0.8em;
    overflow: hidden;
    aspect-ratio: 3 / 4;
    width: 10.1em;
}   
.cart__item__img > img{
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    object-position: center;
}

.cart__item__details{
    align-items: center;
    display: grid; 
    grid-template-columns: 2fr 3fr;
}

.col__information__content{
    min-width: 0;
    gap: 1em;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    box-shadow: none;
    padding: 0;
}
.cart__item__details .product-size__item {
    width: 3em;
    aspect-ratio: 1 / 1;
    height: auto;
    font-size: 1.5em;
}

.cart__item__title{
    font-weight: 500;
} 

.product-colors {
    /* overflow-x: auto; */
    -ms-overflow-style: none;     /* IE & Edge */
    scrollbar-width: none;        /* Firefox */
}

.product-colors::-webkit-scrollbar {
    display: none;                /* Chrome, Safari */
}

.product-color {
    flex: 0 0 auto;
}

.cart__item__content.product__info{
    padding: 0;
    background-color: transparent;
    box-shadow: none;
}

.item-detail_price-quantity{
    align-items: center;
    display: grid; 
    grid-template-columns: 1fr 2fr;
}

.item-detail_label{
    display: none;
}

.col__price{
    font-size: 1.6em;
}

.cart__item__summary{
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.cart__item__total{
    font-weight: 500;
    font-size: 1.6em;
    width: 50%;
}

.cart__item__action{
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--10px);
    background-color: var(--black);
    border-radius: 50%;
    width: 4.2em;
    height: 4.2em;
    padding: 1em;
}

.cart__item__icon{
    width: 100%;
    height: 100%;
    fill: var(--white);
}

.cart__summary {
    font-size: var(--10px);
    width: 30%;
}

.cart__summary__title{
    font-size: 2em;
    height: fit-content;
    margin-bottom: 0.5em;
}

.cart-summary__box{
    border-radius: 3em;
    border: 0.1em solid var(--secondary);
    padding: 3.5em 3em;
}

.cart-vouchers__button, .cart-vouchers__show {
    border: 0.1em solid var(--secondary);
    border-radius: 0.625em;
    width: 100%;
    padding: 0.75em 1.875em;
    background-color: transparent;
    font-weight: 500;
    font-size: 1.6em;
    color: var(--black);
    margin-top: 1.2em;
}

.cart-vouchers__show{
    display: flex;
    align-items: center;
    gap: 0.625em;
}

.cart-vouchers__icon{
    width: 3.125em;
    height: 3.125em;
    opacity: 0.6;
}

.cart-vouchers__selected{
    margin-top: 1.8em;
}

.cart-summary__save{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    margin-top: 1.8em;
}

.cart-save__icon{
    width: 3.2em;
    height: 3.2em;
    margin-right: 1em;
    fill: green;
}

.cart-summary__save > span{
    font-size: 1.6em;
}

.cart-summary__total{
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2em 0 2em;
    gap: 2em;
    border-top: 0.1em solid rgba(0, 0, 0, 0.2);
    margin-top: 2em;
}

.cart-summary__total-row{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cart-summary__total-row > span{
    font-size: 1.4em;
}

.cart-summary__total-row > strong{
    font-size: 1.4em;
}

.cart-summary__total-row:last-child{
    font-size: 1.8em;
    font-weight: 600;
    color: var(--primary);
}
.cart-summary__total-row:last-child{
    font-size: 1.8em;
    font-weight: 600;
    color: var(--primary);
}




.cart-order__button{
    font-size: 1.6em;
    width: 100%;
    padding: 1em 3em;
    font-weight: 500;
    line-height: 1.2;
    border-radius: 1em;
}


/* -------- Empty cart -------- */
.cart__empty{
    font-size: var(--10px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1em;
}

.cart__empty > img {
    width: 35em;
    height: 35em;
}

.cart__empty > h3 {
    font-size: 2.2em;
}

.cart__empty > p, .cart__empty > a {
    font-size: 1.6em;
}

.cart__empty > a {
    margin-top: 0.5em;
}

/* -------- Responsive -------- */
@media (max-width: 575px){   
    .cart__empty > a {
        font-size: 2em;
        margin-top: 0;
    }
}

@media (max-width: 768px){  
    .page-banner{
        height: 14em;
    }

    .page-banner__title{
        font-size: 1.8em;
    }

    .page-banner__breadcrumb-list{
        font-size: 1.3em;
    }

    .cart__notification-sold > .icon-fire{
        width: 3.8em;
        height: 3.8em;
    }

    .cart__wrapper{
        flex-direction: column;
        padding-top: 4em;
    }

    .cart__content, .cart__summary{
        width: 100%;
    }

    .notification-sold__count{
        font-size: 1.35em;
    }
    
    .cart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .cart-header__hidden{
        display: none;
    }

    .cart__item{
        gap: 2em;
    }

    .cart__item__details{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 1em;
    }

    .cart__item__title > a{
        font-size: 1.2em;
    }

    .item-detail_quantity-wrapper{
        margin-left: auto;
    }

    .cart__item__summary{
        display: flex;
        flex-direction: column-reverse;
        gap: 2em;
        height: 100%;
        margin-left: auto;
    }

    .item-detail_price-quantity{
        gap: 2em;
    }

    .item-detail_quantity-wrapper, .item-detail_price-wrapper{
        display: flex;
        align-items: center;
        line-height: normal;
        gap: 1em;
        width: 100%;
    }

    .item-detail_label{
        display: inline-block;
        font-size: 1.4em;
    }

    .col__price{
        font-weight: 800;
    }

    .cart__item__total{
        font-weight: 800;
        width: 100%; 
    }

    
    .cart-summary__box{
        padding: 2.5em 1.5em;
    }

    .cart__summary__title{
        font-size: 3.2em;
    }

    .freeship-icon__amount{
        font-size: 1em;
    }

    .freeship_progress__bar-end{
        font-size: 1.2em;
    }

    .freeship-message-need, .freeship-message-success{
        font-size: 1.3em;
    }

    .btn-open-vouchers{
        font-size: 1.9em;
    }

    .cart-vouchers__button {
        padding: 1.52em 1.875em;
    }

    .cart-vouchers__show{
        padding: 1em 1.875em;
        justify-content: center;
    }

    .cart-vouchers__show > span{
        font-size: 1.2em;
    }

    .cart-summary__total-row > span, .cart-summary__total-row > strong{
        font-size: 1.6em;
    }

    .cart-order__button {
        font-size: 2.2em;
        padding-top: 0.9em;
        padding-bottom: 0.9em;
    }
}