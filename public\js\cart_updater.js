// OffcanvasCartProductItemCmp and CartProductItemCmp will be available globally

const cartWrapper = document.querySelector("#cart");
const cartSidebarWrapper = document.querySelector("#cartSidebar");
const SHIPPING_FEE = "30.000đ";

const cartUpdater = (function () {
  const renderInitCartOffcanvas = (wrapper) => {
    const cartList = document.querySelector("#cartSidebar .cart__list");
    if (!cartList) return;

    const cartItems = JSON.parse(localStorage.getItem("cartItems") || "[]");
    if (cartItems.length === 0) return;

    cartList.innerHTML = "";

    cartItems.forEach(item => {
      //Gán lại isActive theo selectedSize / selectedColor
      item.sizes = item.sizes.map(size => ({
        ...size,
        isActive: size.text === item.selectedSize
      }));

      item.colors = item.colors.map(color => ({
        ...color,
        isActive: color.backgroundColor === item.selectedColor
      }));

      // Ensure isGift flag is preserved (for backward compatibility)
      if (item.isGift === undefined) {
        item.isGift = false;
      }

      if (typeof OffcanvasCartProductItemCmp === 'function') {
        cartList.insertAdjacentHTML("beforeend", OffcanvasCartProductItemCmp(item));
      }
    });

    let subtotal = renderInitSubtotal(cartItems, wrapper)
    updateFreeshipBar(cartSidebarWrapper, subtotal);
    updateProductCount(wrapper);
    updateCartCount(cartItems); // icon header
    cartUpdater.rebind("#cartSidebar");
  };

  const renderInitCart = (wrapper) => {
    if (!wrapper) return;

    const container = wrapper.querySelector(".products-cart");
    if (!container) return;

    const cartContent = wrapper.querySelector(".cart__content");
    const cartSummary = wrapper.querySelector(".cart__summary");
    const cartEmpty = wrapper.querySelector(".cart__empty");

    const cartItems = JSON.parse(localStorage.getItem("cartItems") || "[]");
    if (cartItems.length === 0) {

      cartContent?.classList.add("hidden");
      cartSummary?.classList.add("hidden");
      cartEmpty?.classList.remove("hidden");
      return;
    };

    cartContent?.classList.remove("hidden");
    cartSummary?.classList.remove("hidden");
    cartEmpty?.classList.add("hidden");

    // Xoá các sản phẩm cũ nếu có
    const existingItems = container.querySelectorAll(".cart__item.product__item.product");
    existingItems.forEach(el => el.remove());

    [...cartItems].reverse().forEach(item => {
      // Ensure isGift flag is preserved
      if (item.isGift === undefined) {
        item.isGift = false;
      }

      const unitPrice = parseCurrency(item.price || "0");
      const subtotal = formatCurrency(unitPrice * item.quantity);

      let html;
      if (typeof CartProductItemCmp === 'function') {
        html = CartProductItemCmp(item, subtotal);
      } else {
        return;
      }

      // Thêm sau .cart-header
      const cartHeader = container.querySelector(".cart-header");
      cartHeader.insertAdjacentHTML("afterend", html);
    });

    let subtotal = renderInitSubtotal(cartItems, wrapper)
    updateFreeshipBar(wrapper, subtotal);
    renderTotalPrice(wrapper, subtotal);
    cartUpdater.rebind("#cart")
  };

  const bindQuantityButtons = (wrapper) => {
    const minusBtns = wrapper.querySelectorAll(".quantity-cart__btn-down");
    const plusBtns = wrapper.querySelectorAll(".quantity-cart__btn-up");
    const quantityInputs = wrapper.querySelectorAll(".quantity-cart__input");

    minusBtns.forEach((btn) =>
      btn.addEventListener("click", () => {
        const input = btn.closest(".quantity-cart")?.querySelector(".quantity-cart__input");
        updateQuantity(wrapper, input, -1);
      }));

    plusBtns.forEach((btn) =>
      btn.addEventListener("click", () => {
        const input = btn.closest(".quantity-cart")?.querySelector(".quantity-cart__input");
        updateQuantity(wrapper, input, 1);
      })
    );

    quantityInputs.forEach((input) => {
      input.addEventListener("change", () => updateQuantity(wrapper, input));
    });
  };

  const bindDeleteButtons = (wrapper) => {
    const deleteBtns = wrapper.querySelectorAll(".cart__item__delete");
    deleteBtns.forEach((btn) => {
      btn.addEventListener("click", (e) => {
        e.stopPropagation();
        const item = btn.closest(".cart__item");
        if (item) {
          item.remove();
          updateCartSummary(wrapper);
          syncCartToLocalStorage(wrapper);
        }
      });
    });
  };

  const addProductToCart = () => {
    document.addEventListener("click", function (e) {
      const btn = e.target.closest(".btn_add_to_cart");
      if (!btn) return;

      // Skip if this is the main product detail page (product_detail.js)
      const isMainProductDetail = btn.closest('.product-detail__half-layout.product');
      if (isMainProductDetail) return;

      const productEl = btn.closest(".product");
      if (!productEl) return;

      // 1. Thông tin chính
      const productId = btn.dataset.productId || "";
      if (!productId) {
        alert("Sản phẩm không hợp lệ");
        return;
      }
      const name = productEl.querySelector(".product__title > a")?.textContent.trim() || "";
      const imageEl = productEl.querySelector(".product__img");
      const image = imageEl?.getAttribute("src") || "";
      const price = productEl.querySelector(".sale-price")?.textContent.trim() || "";

      // 2. Danh sách size (có thể chứa `data-color`)
      const sizeElements = productEl.querySelectorAll(".product-size__item");
      const sizes = Array.from(sizeElements).map((el) => ({
        text: el.textContent.trim(),
        isActive: el.classList.contains("product-size--active"),
        dataColor: el.getAttribute("data-color") || null,
      }));

      // 3. Danh sách màu
      // Lấy size đang active
      const activeSizeEl = productEl.querySelector(".product-size__item.product-size--active");
      let colors = [];

      if (activeSizeEl) {
        const rawDataColor = activeSizeEl.getAttribute("data-color");

        try {
          const parsed = JSON.parse(rawDataColor || "[]");

          // 3. Lấy màu đang active từ HTML
          const activeColorEl = productEl.querySelector(".product-color.active");
          const activeColorImg = activeColorEl?.getAttribute("data-img");

          // 4. Parse danh sách màu và đánh dấu isActive
          colors = parsed.map((item) => ({
            backgroundColor: item.color,
            dataImg: item.img,
            isActive: item.img === activeColorImg,
          }));
        } catch (e) {
          console.error("Không parse được data-color:", e);
        }
      }


      // 4. Số lượng
      const quantityInput = productEl.querySelector(".quantity-input");
      const quantity = quantityInput ? parseInt(quantityInput.value || "1", 10) : 1;

      const productData = {
        product_id: productId,
        name,
        image,
        sizes,
        colors,
        quantity,
        price
      };

      addToCartSmart(productData);
      updateCartSummary(cartSidebarWrapper);

      cartSidebarWrapper?.classList.add("is-open");
      const modal = btn.closest("#product-modal");
      if (modal) {
        modal.classList.add("product-modal--hidden");
        document.body.classList.remove("modal-open");
      }
    });
  };

  function addToCartSmart(productData) {
    const cartList = document.querySelector("#cartSidebar .cart__list");
    if (!cartList) return;

    let cartItems = JSON.parse(localStorage.getItem("cartItems") || "[]");

    // Lấy size và color đang được chọn
    const selectedSize = productData.sizes.find(size => size.isActive)?.text || "";
    const selectedColor = productData.colors.find(color => color.isActive)?.backgroundColor || "";

    // Gắn thêm vào dữ liệu để so sánh và lưu lại
    productData.selectedSize = selectedSize;
    productData.selectedColor = selectedColor;

    // Kiểm tra trùng sản phẩm
    const matchedIndex = cartItems.findIndex(item =>
      item.product_id === productData.product_id &&
      item.selectedSize === selectedSize &&
      item.selectedColor === selectedColor
    );

    if (matchedIndex !== -1) {
      // Nếu trùng thì tăng số lượng
      cartItems[matchedIndex].quantity += productData.quantity;
    } else {
      // Nếu mới thì thêm vào
      cartItems.push(productData);
    }

    localStorage.setItem("cartItems", JSON.stringify(cartItems));
    renderInitCartOffcanvas(cartSidebarWrapper);
  }

  // Gift functionality
  const showGiftAddedMessage = (message) => {
    let notification = document.querySelector('.gift-notification');
    if (!notification) {
      notification = document.createElement('div');
      notification.className = 'gift-notification';
      document.body.appendChild(notification);
    }

    notification.textContent = message;
    notification.classList.add('show');

    setTimeout(() => {
      notification.classList.remove('show');
    }, 3000);
  };

  const addGiftProductToCart = (giftData) => {
    if (!giftData || !giftData.product_id) {
      return;
    }

    // Use existing addToCartSmart function
    addToCartSmart(giftData);
    showGiftAddedMessage('Đã thêm sản phẩm tặng vào giỏ hàng!');

    // Open cart sidebar
    const cartSidebarWrapper = document.querySelector('#cartSidebar');
    if (cartSidebarWrapper) {
      cartSidebarWrapper.classList.add('is-open');
    }
  };

  return {
    init(selector) {
      const wrapper = document.querySelector(selector);
      if (!wrapper) return false;
      renderInitCartOffcanvas(cartSidebarWrapper);
      renderInitCart(cartWrapper);
      changeProductColor(wrapper);
      changeProductSize(wrapper);
      bindDeleteButtons(wrapper);
      addProductToCart();
      return true;
    },
    rebind(selector) {
      const wrapper = document.querySelector(selector);
      if (!wrapper) return;
      bindQuantityButtons(wrapper);
      bindDeleteButtons(wrapper);
      changeProductSize(wrapper);
    },
    renderInitCartOffcanvas: renderInitCartOffcanvas,
    addToCartSmart: addToCartSmart,
    addGiftProductToCart: addGiftProductToCart,
    showGiftAddedMessage: showGiftAddedMessage
  };
})();

document.addEventListener("DOMContentLoaded", () => {
  cartUpdater.init("#cartSidebar");
  cartUpdater.init("#cart");
});

function renderInitSubtotal(cartItems, wrapper) {
  const subtotalPrice = cartItems.reduce((subtotal, item) => {
    const unitPrice = parseCurrency(item.price || "0");
    return subtotal + unitPrice * item.quantity;
  }, 0);

  const subTotalEl = wrapper.querySelector('[data-type="subtotal"]');
  if (subTotalEl) {
    subTotalEl.textContent = formatCurrency(subtotalPrice);
  }

  return subtotalPrice;
}

function updateFreeshipBar(wrapper, current) {
  const freeshipBox = wrapper.querySelector(".freeship_progress");
  if (!freeshipBox) return;

  const target = parseInt(freeshipBox.dataset.target || "0", 10);
  const needMsg = freeshipBox.querySelector(".freeship-message-need");
  const successMsg = freeshipBox.querySelector(".freeship-message-success");
  const valueBar = freeshipBox.querySelector(".freeship_progress__bar-value");
  const amountText = freeshipBox.querySelector(".freeship-icon__amount");
  const endText = freeshipBox.querySelector(".freeship_progress__bar-end");
  var shippingEl = document.querySelector('[data-type="shipping"]');

  if (valueBar) {
    const progress = Math.min((current / target) * 100, 100);
    valueBar.style.width = `${progress}%`;
  }

  if (amountText) {
    amountText.textContent = formatCurrency(current);
  }

  if (endText) {
    endText.textContent = formatCurrency(target);
  }

  const isFreeShipping = current >= target;
  toggleVisibility(successMsg, isFreeShipping, "inline-block");
  toggleVisibility(needMsg, !isFreeShipping);

  // Cập nhật giá trị cần đạt
  if (!isFreeShipping && needMsg) {
    const remaining = target - current;
    const bold = needMsg.querySelector("b");
    if (bold) bold.textContent = formatCurrency(remaining);
  }

  // Cập nhật phí vận chuyển
  if (shippingEl) {
    shippingEl.textContent = isFreeShipping ? "Miễn phí" : SHIPPING_FEE;
  }
}

const updateQuantity = (wrapper, input, delta = 0) => {
  if (!input) return;

  let quantity = parseInt(input.value || "1", 10);

  if (delta !== 0) {
    quantity = Math.max(1, quantity + delta);
  } else {
    if (isNaN(quantity) || quantity < 1) quantity = 1;
  }

  input.value = quantity;

  const cartItem = input.closest(".cart__item");
  const priceEl = cartItem?.querySelector(".cart__item__price--sale");
  const totalEl = cartItem?.querySelector(".product-total");

  if (priceEl && totalEl) {
    const unitPrice = parseCurrency(priceEl.textContent || "0");
    const total = quantity * unitPrice;
    totalEl.textContent = formatCurrency(total);
  }

  updateCartSummary(wrapper);
  syncCartToLocalStorage(wrapper);
};

const updateCartSummary = (wrapper) => {
  const items = wrapper.querySelectorAll(".cart__item");
  let total = 0;
  const cartItems = [];

  items.forEach((item) => {
    const priceText = item.querySelector(".cart__item__price--sale")?.textContent || "0";
    const input = item.querySelector(".quantity-cart__input") || item.querySelector(".cart__item__quantity--number span");
    const quantity = parseInt(input?.value || input?.textContent || "1", 10);
    const price = parseCurrency(priceText);
    total += quantity * price;

    cartItems.push({
      quantity
    });
  });

  renderSubTotalPrice(wrapper, total);
  updateFreeshipBar(wrapper, total);
  updateProductCount(wrapper);
  updateCartCount(cartItems);
  renderTotalPrice(wrapper, total);

  const freeshipBox = wrapper.querySelector(".freeship_progress");
  if (freeshipBox) freeshipBox.setAttribute("data-current", total);
};

const renderSubTotalPrice = (wrapper, total) => {
  var subTotalEl = wrapper.querySelector('[data-type="subtotal"]');
  if (total === 0) {
    subTotalEl.textContent = 0;
    return;
  }
  if (subTotalEl) {
    subTotalEl.textContent = formatCurrency(total);
  }
};

const updateProductCount = (wrapper) => {
  const countEl = wrapper.querySelector(".cart__total__count");
  const items = wrapper.querySelectorAll(".cart__item");
  let totalQuantity = 0;

  items.forEach((item) => {
    const input = item.querySelector(".quantity-cart__input") || item.querySelector(".cart__item__quantity--number span");
    const quantity = parseInt(input?.value || input?.textContent || "1", 10);
    totalQuantity += quantity;
  });

  if (countEl) countEl.textContent = totalQuantity;
};

const renderTotalPrice = (wrapper, total) => {
  var shippingEl = wrapper.querySelector('[data-type="shipping"]');
  var discountEl = wrapper.querySelector('[data-type="discount"]');
  var totalPriceEl = wrapper.querySelector('[data-type="total"]');

  if (!totalPriceEl) return;

  if (total === 0) {
    shippingEl.textContent = 0;
    discountEl.textContent = 0;
    totalPriceEl.textContent = 0;
    return;
  }

  let shippingFee = 0;
  let discountAmount = 0;

  if (shippingEl) {
    const shippingText = shippingEl.textContent.trim();
    if (shippingText !== "Miễn phí") {
      shippingFee = parseInt(shippingText.replace(/[^\d]/g, ''), 10) || 0;
    }
  }

  if (discountEl) {
    const discountText = discountEl.textContent.trim();
    const numeric = parseInt(discountText.replace(/[^\d]/g, ''), 10);

    if (!isNaN(numeric) && numeric > 0) {
      discountAmount = numeric;
    }
  }

  const finalTotal = total - shippingFee - discountAmount;
  totalPriceEl.textContent = formatCurrency(finalTotal);
};

/**
 * Cập nhật giỏ hàng vào localStorage
 */
const syncCartToLocalStorage = (wrapper) => {
  const updatedCart = [];

  wrapper.querySelectorAll(".cart__item").forEach((itemEl) => {
    // 1) Thông tin cơ bản
    const productId = itemEl.getAttribute("data-id") || "";
    const name = itemEl.querySelector(".cart__item__title > a")?.textContent.trim() || "";
    const image = itemEl.querySelector(".product__img")?.getAttribute("src") || "";
    const price = itemEl.querySelector(".cart__item__price--sale")?.textContent.trim() || "";
    const quantity = parseInt(itemEl.querySelector(".quantity-cart__input")?.value || "1", 10);

    // 2) Lấy size & color đang active (QUAN TRỌNG để nhận diện sản phẩm)
    const selectedSize = itemEl.querySelector(".product-size--active")?.textContent.trim() || "";
    const selectedColorSpan = itemEl.querySelector(".product-color.active .product-color__item");
    const currentStyle = selectedColorSpan?.getAttribute("style") || "";
    const selectedColor = currentStyle
      .split("background-color:")[1]
      ?.split(";")[0]
      ?.trim() || "";


    // 3) Danh sách size/color đầy đủ (để render lại về sau)
    const sizes = Array.from(itemEl.querySelectorAll(".product-size__item")).map((el) => ({
      text: el.textContent.trim(),
      isActive: el.classList.contains("product-size--active"),
      dataColor: el.getAttribute("data-color") || null,
    }));

    const colors = Array.from(itemEl.querySelectorAll(".product-color")).map((el) => ({
      backgroundColor: el.querySelector(".product-color__item").getAttribute("style").split("background-color:")[1]
        ?.split(";")[0]
        ?.trim() || "",
      dataImg: el.getAttribute("data-img") || "",
      isActive: el.classList.contains("active"),
    }));

    // 4) Check if this is a gift item
    const isGift = itemEl.classList.contains("gift-item");

    // 5) Push vào mảng cart
    updatedCart.push({
      product_id: productId,
      name,
      image,
      price,
      quantity,
      selectedSize,
      selectedColor,
      sizes,
      colors,
      isGift: isGift, // Preserve gift flag
    });
  });

  localStorage.setItem("cartItems", JSON.stringify(updatedCart));
};

function updateCartCount(cartItems) {
  const countEl = document.querySelector(".cart-btn__count");
  if (!countEl) return;

  // Handle case where cartItems is undefined or not an array
  if (!cartItems || !Array.isArray(cartItems)) {
    cartItems = JSON.parse(localStorage.getItem("cartItems") || "[]");
  }

  const totalQuantity = cartItems.reduce((sum, item) => sum + (parseInt(item.quantity, 10) || 0), 0);

  if (totalQuantity > 0) {
    countEl.textContent = totalQuantity;
    countEl.classList.remove('hidden');
  } else {
    countEl.classList.add('hidden');
  }
}

const changeProductColor = (wrapper) => {
  wrapper.addEventListener("click", (e) => {
    const color = e.target.closest(".product-color");
    if (!color || color.classList.contains("product-color--more")) return;

    const img = color.getAttribute("data-img");
    const product = color.closest(".product");
    const mainImg = product.querySelector(".product__img");

    // Xóa trạng thái active cũ
    const allColors = product.querySelectorAll(".product-color");
    allColors.forEach((c) => c.classList.remove("active"));
    color.classList.add("active");

    if (mainImg && img) {
      mainImg.setAttribute("src", img);
    }

    syncCartToLocalStorage(wrapper);
  });
};

const changeProductSize = (wrapper) => {
  const sizes = wrapper.querySelectorAll(".product-size__item");
  sizes.forEach((item) => {
    item.addEventListener("click", (el) => {
      if (!el.target.dataset.color) return;

      // Skip if this is the main product detail page
      const isMainProductDetail = el.currentTarget.closest('.product-detail__half-layout.product');
      if (isMainProductDetail) return;

      let colorItems = [];
      try {
        colorItems = JSON.parse(el.target.dataset.color);
        // Ensure it's an array
        if (!Array.isArray(colorItems)) {
          console.warn('data-color is not an array:', el.target.dataset.color);
          return;
        }
      } catch (error) {
        console.error('Error parsing data-color:', el.target.dataset.color, error);
        return;
      }
      const productWrapper = el.currentTarget.closest(".product");
      const productInfo = productWrapper.querySelector(".product__info");
      const productColors = productInfo.querySelector(".product-colors");

      const currentActiveColor = productColors.querySelector(
        ".product-color.active"
      );
      const currentImg = currentActiveColor?.getAttribute("data-img");

      let colorHTML = "";

      colorItems.forEach((colorItem) => {
        colorHTML +=
          `<li class="product-color" data-img="${colorItem.img}">
            <span class="product-color__item" style="background-color: ${colorItem.color};"></span>
          </li>`;
      });

      productColors.innerHTML = colorHTML;

      const newColorElements = productColors.querySelectorAll(".product-color");
      let foundMatch = false;

      newColorElements.forEach((colorEl) => {
        if (colorEl.getAttribute("data-img") === currentImg) {
          colorEl.classList.add("active");
          foundMatch = true;
        }
      });

      if (!foundMatch && newColorElements.length > 0) {
        newColorElements[0].classList.add("active");
      }

      const activeColor = productColors.querySelector(
        ".product-color.active"
      );
      const newImg = activeColor?.getAttribute("data-img");
      const mainImg = productWrapper.querySelector(".product__img");
      if (mainImg && newImg) {
        mainImg.setAttribute("src", newImg);
      }

      const activeSize = productWrapper.querySelector(
        ".product-size--active"
      );
      if (activeSize) {
        activeSize.classList.remove("product-size--active");
      }
      el.currentTarget.classList.add("product-size--active");

      changeProductColor(wrapper);
      syncCartToLocalStorage(wrapper);
    });
  });
};

function parseCurrency(text) {
  return parseInt(text.replace(/[^\d]/g, ""), 10) || 0;
}

function formatCurrency(number) {
  return number.toLocaleString("vi-VN") + "đ";
}

function toggleVisibility(el, show, display = "block") {
  if (!el) return;
  el.classList.toggle("hidden", !show);
  el.style.display = show ? display : "none";
}


