/* News Detail Page Styles */

/* News Detail Background */
.news-detail-bg {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 18.75em;
  position: relative;
  display: flex;
  align-items: center;
  padding: 5em 0;
  font-size: var(--10px);
}

/* .news-detail-bg--overlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
} */

.news-detail-bg .container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 75em;
  margin: 0 auto;
  padding: 0 0.9375em;
}

.news-detail-bg .content {
  margin-bottom: 1.875em;
  text-align: left;
  color: white;
}

.news-detail-bg .title {
  font-size: 3.125em;
  font-weight: 700;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  color: white;
}

/* News Detail Breadcrumb */
.news-detail-breadcrumb {
  background: var(--bg-color-body);
  padding: 1.25em 0;
  border-bottom: 1px solid #e9ecef;
  font-size: var(--10px);
}

.news-detail-breadcrumb .list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5em;
}

.news-detail-breadcrumb .item {
  display: flex;
  align-items: center;
  font-size: 1.125em;
  color: var(--color-body);
  font-weight: 400;
}

.news-detail-breadcrumb .item:not(:last-child)::after {
  content: "";
  width: 1em;
  height: 1em;
  margin-left: 0.5em;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9,18 15,12 9,6'%3E%3C/polyline%3E%3C/svg%3E")
    no-repeat center;
  background-size: contain;
}

.news-detail-breadcrumb .link {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);
}

.news-detail-breadcrumb .link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* News Detail Main */
.news-detail-main {
  padding: 3em 0;
  background: var(--bg-color-body);
  font-size: var(--10px);
}

.news-detail-wrapper {
  background: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Article Styles */
.news-detail-article {
  padding: 3em;
}

/* Article Header */
.article-header {
  margin-bottom: 2.5em;
  padding-bottom: 2em;
  border-bottom: 1px solid #e9ecef;
}

.article-title {
  font-size: 2.8em;
  font-weight: 700;
  color: var(--text-dark);
  line-height: 1.3;
  margin: 0 0 1.5em 0;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1em;
}

.meta-info {
  display: flex;
  gap: 2em;
  align-items: center;
}

.publish-date,
.author {
  display: flex;
  align-items: center;
  gap: 0.5em;
  color: var(--color-body);
  font-size: 1.4em;
}

.publish-date .icon,
.author .icon {
  opacity: 0.7;
}

/* Social Share */
.social-share {
  display: flex;
  align-items: center;
  gap: 1em;
}

.share-label {
  font-size: 1.3em;
  color: var(--color-body);
  font-weight: 500;
}

.share-buttons {
  display: flex;
  gap: 0.5em;
}

.share-btn {
  width: 3.5em;
  height: 3.5em;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.share-btn.facebook {
  background: #1877f2;
  color: white;
}

.share-btn.twitter {
  background: #1da1f2;
  color: white;
}

.share-btn.copy {
  background: #6c757d;
  color: white;
}

.share-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Article Content */
.article-content {
  font-size: 1.6em;
  line-height: 1.7;
  color: var(--color-body);
}

.article-summary {
  background: #f8f9fa;
  padding: 2em;
  border-left: 4px solid var(--primary);
  margin-bottom: 2.5em;
  border-radius: 0 8px 8px 0;
}

.article-summary p {
  margin: 0;
  font-size: 1.1em;
}

.article-image {
  margin: 2.5em 0;
  text-align: center;
}

.article-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.article-image figcaption {
  margin-top: 1em;
  font-style: italic;
  color: #6c757d;
  font-size: 0.9em;
}

.article-body h2 {
  font-size: 1.8em;
  font-weight: 700;
  color: var(--text-dark);
  margin: 2.5em 0 1.5em 0;
  padding-bottom: 0.5em;
  border-bottom: 2px solid var(--primary);
}

.article-body h3 {
  font-size: 1.5em;
  font-weight: 600;
  color: var(--text-dark);
  margin: 2em 0 1em 0;
}

.article-body h4 {
  font-size: 1.3em;
  font-weight: 600;
  color: var(--text-dark);
  margin: 1.5em 0 1em 0;
}

.article-body p {
  margin: 1.5em 0;
}

.article-body ul,
.article-body ol {
  margin: 1.5em 0;
  padding-left: 2em;
}

.article-body li {
  margin: 0.8em 0;
}

.article-body strong {
  color: var(--text-dark);
  font-weight: 600;
}

/* Highlight Box */
.highlight-box {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 2em;
  margin: 2em 0;
}

.highlight-box h4 {
  color: var(--primary);
  margin-top: 0;
  margin-bottom: 1em;
  font-size: 1.4em;
}

/* Info Table */
.info-table {
  margin: 2.5em 0;
  overflow-x: auto;
}

.info-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.info-table th,
.info-table td {
  padding: 1.2em;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.info-table th {
  background: var(--primary);
  color: white;
  font-weight: 600;
  font-size: 1.1em;
}

.info-table tr:last-child td {
  border-bottom: none;
}

.info-table tr:nth-child(even) {
  background: #f8f9fa;
}

/* Conclusion Box */
.conclusion-box {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 1px solid #c8e6c9;
  border-radius: 8px;
  padding: 2.5em;
  margin: 3em 0;
}

.conclusion-box h3 {
  color: #2e7d32;
  margin-top: 0;
  margin-bottom: 1.5em;
  font-size: 1.6em;
}

/* Article Footer */
.article-footer {
  margin-top: 3em;
  padding-top: 2em;
  border-top: 1px solid #e9ecef;
}

.tags {
  display: flex;
  align-items: center;
  gap: 1em;
  flex-wrap: wrap;
}

.tags-label {
  font-weight: 600;
  color: var(--text-dark);
  font-size: 1.4em;
}

.tag-list {
  display: flex;
  gap: 0.8em;
  flex-wrap: wrap;
}

.tag {
  background: var(--primary);
  color: white;
  padding: 0.5em 1em;
  border-radius: 20px;
  font-size: 1.2em;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 992px) {
  .news-detail-bg {
    padding: 3em 0 2em;
    min-height: 15em;
    font-size: 1.25vw;
  }

  .news-detail-bg .title {
    font-size: 2.5em;
  }

  .news-detail-bg .container {
    padding: 0 0.625em;
  }

  .news-detail-breadcrumb {
    font-size: 1.25vw;
  }
}

@media (max-width: 768px) {
  .news-detail-bg {
    padding: 2.5em 0 1.25em;
    min-height: 12.5em;
    font-size: 2vw;
  }

  .news-detail-bg .title {
    font-size: 1.75em;
  }

  .news-detail-breadcrumb {
    font-size: 2vw;
    padding: 1em 0;
  }

  .news-detail-breadcrumb .item {
    font-size: 1em;
  }

  .news-detail-main {
    padding: 2em 0;
    font-size: 1.25vw;
  }

  .news-detail-article {
    padding: 2em;
  }

  .article-title {
    font-size: 2.2em;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5em;
  }

  .meta-info {
    gap: 1.5em;
  }

  .article-content {
    font-size: 1.4em;
  }

  .info-table th,
  .info-table td {
    padding: 1em;
  }
}

@media (max-width: 576px) {
  .news-detail-bg {
    padding: 2em 0 1em;
    min-height: 10em;
    font-size: 2.5vw;
  }

  .news-detail-bg .title {
    font-size: 1.5em;
  }

  .news-detail-breadcrumb {
    font-size: 2.5vw;
    padding: 0.75em 0;
  }

  .news-detail-breadcrumb .item {
    font-size: 0.9em;
  }

  .news-detail-breadcrumb .item:not(:last-child)::after {
    width: 0.8em;
    height: 0.8em;
  }

  .news-detail-content {
    padding: 2em 0;
    font-size: 2vw;
  }

  .news-detail-content__title {
    font-size: 2em;
  }

  .news-detail-meta {
    gap: 0.8em;
    font-size: 1.1em;
  }

  .news-detail-body {
    font-size: 1.3em;
  }
}

/* Content news detail */
@media (min-width: 1024px) {
  .container.container-detail {
    max-width: 960px !important;
    margin: 0 auto;
    padding: 0 !important;
    margin-top: 2vw;
  }
}
@media (max-width: 1099px) {
  .root-container {
    overflow-y: unset !important;
  }
}
@media (min-width: 1100px) {
  .root-container {
    gap: unset;
  }
}

:root {
  --color-white: #ffffff;
  --color-black: #000000;
  --black-rgb: 0, 0, 0;
  --color-gray: 204, 13, 57;
  --color-yellow: 255, 222, 0;
  --color-green: 0, 126, 130;
}

html {
  scroll-behavior: smooth;
  font-size: 62.5%;
}

.root-container {
  justify-content: unset !important;
}

.border {
  border: 1px solid #000;
}
.mb-1 {
  margin-bottom: 1rem !important;
}

.mt-2 {
  margin-top: 2rem !important;
}

.mb-2 {
  margin-bottom: 2rem !important;
}

.mb-3 {
  margin-bottom: 3rem !important;
}

.mb-4 {
  margin-bottom: 4rem !important;
}

.my-2 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.my-3 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.px-2 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.fs-10 {
  font-size: 1em;
}
.fs-12 {
  font-size: 1.2em;
}
.fs-13 {
  font-size: 1.3em;
}
.fs-14 {
  font-size: 1.4em;
}
.fs-16 {
  font-size: 1.6em;
}

.ff-unbounded {
  font-family: "Unbounded", sans-serif;
}

.fw-400 {
  font-weight: 400;
}

.fw-700 {
  font-weight: 700;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

/* --- post detail --- */
.container-detail {
  line-height: 1.2;
  font-size: 1rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 1rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  font-size: 1.3em;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  margin: 0 0.5em;
  font-size: 1.2em;
  line-height: 2em;
}

.title-detail {
  font-size: 2.4rem;
  font-weight: 500;
  line-height: 1.35;
  margin: 0;
  padding: 0;
}

.detail-note {
  font-size: 1rem;
  font-family: "Unbounded", sans-serif;
  margin-bottom: 1em;
}

.detail-des {
  font-size: 1.3em;
  font-weight: 400;
  margin-bottom: 1.5em;
  text-align: justify;
}

.article-meta {
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: 1rem;
  margin-top: 2em;
  display: flex;
  align-items: center;
  gap: 1.5em;
  width: fit-content;
}

.article-meta-item {
  font-size: 1.2em;
  display: flex;
  align-items: center;
  gap: 0.3em;
}
.article-meta-item.active > .article-meta-icon,
.article-meta-item:hover > .article-meta-icon {
  background-color: rgb(var(--color-gray));
}

.article-meta-icon {
  width: 2.5em;
  height: 2.5em;
  border-radius: 50%;
  background-color: rgb(var(--color-gray), 0.2);
  padding: 0.3em;
  position: relative;
  transition: all 0.3s ease-in-out;
}

.article-meta-icon > svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5em;
  height: 1em;
  width: 1em;
}

.article-meta-icon > svg > use {
  stroke: var(--color-black) !important;
  stroke-width: 2px;
}
.article-meta-item.active > .article-meta-icon > svg > use,
.article-meta-item:hover > .article-meta-icon > svg > use {
  stroke: #fff !important;
  stroke-width: 2px;
  fill: #fff !important;
  color: #fff;
}

.article-meta-item > span {
  font-size: 1em;
  font-style: italic;
  font-weight: 400;
}
.detail-img {
  width: 100%;
  height: auto;
  border-radius: 1rem;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
#copyToast {
  visibility: hidden;
  min-width: 140px;
  background-color: var(--neutral-200);
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 8px 12px;
  /* position: fixed; */
  z-index: 1000;
  bottom: 80px; /* vị trí phía trên icon */
  left: 50%;
  transform: translateX(0%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

#copyToast.show {
  visibility: visible;
  opacity: 1;
}

/* ---------------------------------table of content ------------------ */
.table-of-conent-wrapper {
  font-size: 1rem;
  padding: 3em;
  background-color: rgba(var(--color-gray), 0.1);
  border-radius: 1.5em;
  overflow: hidden;
}
.table-of-conent {
  max-height: 25em;
}
.custom-scroll {
  overflow-y: auto;
}

/* scroll bar track */
.custom-scroll::-webkit-scrollbar {
  width: 0.2em;
}

.custom-scroll::-webkit-scrollbar-track {
  background-color: rgba(var(--color-gray), 0.4);
  border-radius: 5em;
}

/* scroll bar thumb */
.custom-scroll::-webkit-scrollbar-thumb {
  background: var(--color-white);
  border-radius: 5em;
  box-shadow: 0 0 0 0.2em var(--color-white);
  transition: all 0.3s ease-in-out;
}

.table-of-conent-title {
  font-size: 2.4em;
  font-weight: 600;
  margin-bottom: 0.5em;
}

.table-of-conent-list {
  counter-reset: section;
  font-size: 1rem;
  padding-left: 2em;
}

.table-of-conent-list > li {
  font-size: 1.3em;
  counter-increment: section;
  list-style: none;
  position: relative;
  margin-bottom: 0.5em;
}

.table-of-conent-list > li > a {
  font-weight: 400;
}
.table-of-conent-list a:hover {
  color: #f86f00;
  text-decoration: none;
}

.table-of-conent-list > li::before {
  content: counter(section) ". ";
  position: absolute;
  left: -1.5em;
}

.table-of-conent-list > li > ul {
  margin-top: 0.5em;
  counter-reset: subsection;
  padding-left: 2em;
}

.table-of-conent-list > li > ul > li {
  counter-increment: subsection;
  list-style: none;
  position: relative;
  margin-bottom: 0.5em;
  padding-right: 1em;
}

.table-of-conent-list > li > ul > li::before {
  content: counter(section) "." counter(subsection) " ";
  position: absolute;
  left: -2em;
}

/* --- content --- */
.content-detail {
  font-size: 1rem;
  line-height: 1.2;
}
.content-detail h2 {
  font-size: 1.8em;
  font-weight: 500;
  margin-bottom: 0.7em;
}

.content-detail h3 {
  font-size: 1.6em;
  font-weight: 400;
  margin-bottom: 0.7em;
}

.content-detail p {
  font-size: 1.4em;
  margin-bottom: 0.7em;
  text-align: justify;
}

.content-detail img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em auto;
  border-radius: 0.5em;
}

.content-detail ul,
.content-detail ol {
  font-size: 1.3em;
  padding-left: 5em;
  margin-bottom: 1em;
}
.content-detail ul li,
.content-detail ol li {
  margin-bottom: 0.3em;
  position: relative;
}

.content-detail h2 + ul,
.content-detail h2 + ol {
  padding-left: 6em;
}

.content-detail ul li::marker {
  color: rgba(var(--color-gray), 0.9);
}

.content-detail blockquote {
  border-left: 4px solid #ccc;
  padding-left: 1em;
  font-style: italic;
  color: #666;
  margin: 1.5em 0;
}

.content-detail table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
  font-size: 0.95em;
}

.content-detail table,
.content-detail th,
.content-detail td {
  border: 1px solid #ddd;
}

.content-detail th,
.content-detail td {
  padding: 0.75em;
  text-align: left;
}

.content-detail iframe,
.content-detail video {
  width: 100%;
  max-width: 100%;
  height: auto;
  margin: 1em 0;
  border-radius: 0.5em;
}

/* .content-detail h2 + p  {
    padding-left: 2em;
}

.content-detail h2 + p + h3{
    padding-left: 1.6em;
}

.content-detail h2 + h3 {
    padding-left: 2em;
}

.content-detail h3 + p {
    padding-left: 4em;
} */

.content-detail a {
  color: rgb(var(--color-gray));
}
.content-detail a:hover {
  color: #f86f00;
  text-decoration: none;
}

/* --- detail mores --- */
.detail-mores {
  font-size: 1rem;
  padding: 0 2em;
  color: rgb(var(--color-gray));
}

.detail-mores-title {
  font-size: 1.6em;
  font-weight: 500;
  margin-bottom: 0.9em;
  color: var(--color-black);
}

.detail-mores-list {
  display: flex;
  flex-direction: column;
  gap: 1em;
  font-weight: 400;
  padding-left: 2em;
  list-style: none;
}

.detail-mores-list > li {
  display: flex;
  align-items: center;
  gap: 0.5em;
  font-size: 1.2em;
  font-weight: 400;
  position: relative;
  padding-left: 1em;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.detail-mores-list > li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: rgb(var(--color-gray));
  font-size: 1.2em;
}

.detail-mores-list > li:hover {
  color: #f86f00;
  transform: translateX(1em);
}

/* --- banner mino --- */
.banner-mino {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 1em;
  font-size: 1rem;
  position: relative;
  padding: 0 2em 0 4em;
  margin-left: 2em;
  margin-right: 2em;
}

.banner-mino::before {
  content: "";
  position: absolute;
  top: 4.5em;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  border-radius: 2em;
  background: linear-gradient(267.1deg, #5cc7be 0%, #009585 100%);
}

.banner-mino-content {
  width: 60%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 2em;
}

.banner-mino-title--small {
  font-size: 2.35em;
  font-weight: 400;
  color: var(--color-white);
}

.banner-mino-title {
  font-size: 4em;
  line-height: 1.1;
  font-weight: 700;
  color: var(--color-white);
  margin-bottom: 0.5em;
}

.banner-mino-btns {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5em;
}

.banner-mino-btn {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.2em;
  font-size: 1em;
  padding: 0.8em 5em;
  border-radius: 4em;
  background-color: rgb(var(--color-yellow));
  cursor: pointer;
  transition: all 0.3s linear;
}

.banner-mino-btn:hover {
  transform: translateY(-3px);
  background-color: var(--secondary-700);
}

.banner-mino-btn span {
  font-size: 1.2em;
  line-height: 1.1;
  font-weight: 500;
}

.banner-mino-img {
  width: 40%;
  height: 90%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* --- detail-reference ---*/
.detail-reference {
  font-size: 1rem;
  padding: 0 2em;
  color: rgba(var(--black-rgb), 0.7);
}

.detail-reference-title {
  font-size: 1.2em;
  font-weight: 500;
  margin-bottom: 1em;
  color: rgba(var(--black-rgb), 0.7);
}
.detail-reference-list {
  font-size: 1em;
  list-style: decimal;
  padding-left: 3em;
}
.detail-reference-list > li {
  margin-bottom: 0.8em;
  padding-left: 0.2em;
  text-align: justify;
  transition: all 0.3s ease-in-out;
}

.detail-reference-list > li:hover {
  color: #f86f00;
  text-decoration: none;
}

/* --- post related ---*/
.posts-related {
  font-size: 1rem;
  padding: 0 2em;
  border-top: 2px solid rgb(var(--color-gray));
}
.posts-related-header {
  padding: 2em 0;
}
.posts-related-title {
  font-size: 1.8em;
  font-weight: 500;
  color: var(--color-gray);
}

.btn-top {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.6em;
  height: 3.6em;
  border-radius: 50%;
  border: 1px solid rgb(var(--color-gray));
  margin: 1.5em auto;
  position: relative;
  transition: all 0.3s ease-in-out;
}

.btn-top > svg {
  width: 3em;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  fill: rgb(var(--color-gray));
}

.btn-top > svg > use {
  stroke: rgb(var(--color-gray));
  stroke-width: 1px;
}

.btn-top:hover {
  background-color: rgb(var(--color-yellow));
}
.btn-top:hover > svg > use {
  stroke: rgba(var(--black-rgb), 0.7);
}
.btn-top:hover > svg {
  fill: rgba(var(--black-rgb), 0.7);
}

.posts-related-list--c4 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1.5em;
  margin-top: 5rem;
  margin-bottom: 3rem;
}

.posts-related-list--c4 > * {
  flex: 0 0 calc(25% - 1.5em);
  box-sizing: border-box;
}

.posts-related-list--grid4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5em;
  /* margin-top: 5rem; */
  margin-bottom: 3rem;
}

/* --- post related item ---*/
.posts-related-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
  gap: 1em;
  border-radius: 1em;
  background-color: rgba(var(--color-gray), 0.1);
  transition: all 0.3s ease-in-out;
}
.posts-related-item:hover {
  background-color: rgb(var(--color-yellow), 0.1);
  transform: translateY(-0.5em);
}

.posts-related-item-img {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.posts-related-item-img img {
  aspect-ratio: 4 / 3;
  overflow: hidden;
  width: 100%;
  object-fit: cover;
  object-position: center;
  border-top-left-radius: 1em;
  border-top-right-radius: 1em;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.posts-related-item-header {
  display: flex;
  flex-direction: column;
  gap: 1em;
  font-size: 1em;
  padding: 0 1em 1em 1em;
}

.posts-related-item-title {
  height: 3.8em;
  font-size: 1.4em;
  font-weight: 400;
}

.posts-related-item-title:hover {
  color: #f86f00;
  text-decoration: none;
}

.posts-related-item-des {
  display: none;
}

.posts-related-item-meta {
  display: flex;
  align-items: center;
  gap: 1em;
  font-size: 1em;
  list-style: none;
  margin: 0;
}

.posts-related-item-meta-item {
  display: flex;
  align-items: center;
  gap: 0.2em;
  font-size: 1em;
  color: rgba(var(--black-rgb), 0.8);
}

.posts-related-item-meta-icon {
  width: 1.7em;
  height: 1.7em;
  position: relative;
  transition: all 0.3s ease-in-out;
}
.posts-related-item-meta-icon > svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5em;
  width: 100%;
  height: auto;
}

.posts-related-item-meta-icon > svg > use {
  stroke: rgb(var(--color-gray));
  stroke-width: 2px;
}

.posts-related-item-meta-item > span {
  font-size: 1em;
  font-style: italic;
  font-weight: 400;
}


/* Reponsive */
@media (max-width: 767px) {   
    .post-detail-header {
        padding: 0 1.3rem;
    }
    .container-detail{
        font-size: 0.925rem;
    }
    .container, .container-fluid {
        width: 100%;
        padding-right: 1.25rem;
        padding-left: 1.25rem;
        margin-right: auto;
        margin-left: auto;
    }
    .breadcrumb-item{
        font-size: 1em;
    }
    .title-detail {
        font-size: 4.5vw;
        margin-top: 1em;
    }

    .detail-note{
        font-size:  clamp(6px, 1.37vw, 8px);        
    }
    .detail-des{
        font-size: clamp(12px, 2.72vw, 13px);   
        font-weight: 300;     
        margin-bottom: 1.5rem;
    }

    .content-detail{   
        font-size:0.92rem;   
        padding: 0;
    }

    .container-detail > ul, .container-detail >p, .container-detail > h1, .container-detail > h2, .container-detail > h3{
        padding: 0 1.3rem;
    }

    .content-detail ul, .content-detail ol {
        padding-left: 3em;
        margin-bottom: 1em;
    }

    .table-of-conent-wrapper{
        padding: 2em;
    }

    .table-of-conent-list{
        font-size: 0.925em;
    }

    .article-meta {
        gap: 1em;
        align-items: flex-start;
        flex-direction: row;
    }

    .article-meta-item {
        font-size: 1em;
    }
    .article-meta-icon {
        width: 3.2em;
        height: 3.2em;
    }

    .detail-mores {
        font-size: 0.925rem;
        padding: 0;
    }
    .detail-mores-title {
        font-size: 1.3em;
    }

    .detail-mores-list > li{
        font-size: clamp(10px, 2.6vw, 12px);
    }

    .banner-mino{
        font-size: 2.4vw;
        margin:0;
        padding: 2em 1em 0 2em;
    }
    .banner-mino::before{
        top: 7vw;
    }

    .banner-mino-title--small{
        font-size: 2.3vw;
    }

    .banner-mino-title{
        font-size: 3.8vw;
    }

    .banner-mino-content{       
        padding-bottom: 1.7vw;
    }   
    .banner-mino-btns{
        gap:0.5em;
    }
    .banner-mino-btn{
        font-size: clamp(6px, 1.7vw, 10px);
        padding: unset;
        width: clamp(100px, 25vw, 300px);
        padding-top: 1vw;
        padding-bottom: 1vw;
    }

    .banner-mino-btn span{
        font-size: clamp(5px, 1.7vw, 11px);
    }

    .posts-related-list--grid4 {
        grid-template-columns: 1fr;
        /* margin-top: 2em; */
        gap:1em;
    }

    .detail-reference{
        font-size: 0.925rem;
        padding: 0;
    }
    .detail-reference-list{
        padding-left: 1.7em;
    }

    /** posts related */
    .posts-related{
        padding: 0;
    }

    .posts-related-item{
        flex-direction: row;      
        gap:0;
    }
    .posts-related-item-img{
        width: 42%;
    }
    .posts-related-item-header{
        width: 68%;
        padding: 0.5em;
        gap:0.8em;
    }
    .posts-related-item-img img{
        border-top-right-radius:0;
        border-bottom-left-radius: 0.5em;
        border-top-left-radius: 0.5em;
    }
    .posts-related-item-title{
        font-size: clamp(10px, 2.72vw, 13px);
        font-weight: 500;
    }
    .posts-related-item-meta{
        display: none;
    }

    .posts-related-item-des{
        display: block;;
        font-size: clamp(8px, 2.28vw, 12px);
        height: 4.9em;

        display: -webkit-box;
        -webkit-line-clamp: 4;
        line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;    
    }
    .footer-hpv{
        padding-left: 0;
        padding-right: 0;
    }
    
}

@media (min-width: 768px) and (max-width: 991px){
    .banner-mino-title--small{
        font-size: 2vw;
    }

    .banner-mino-title{
        font-size: 3.5vw;
    }

    .banner-mino-content{       
        padding-bottom: 1.2vw;
    }

    .banner-mino-btn{
        font-size: 1.3vw;
        padding: unset;
        width: clamp(100px, 25vw, 300px);
        padding-top: 1vw;
        padding-bottom: 1vw;
    }

    .posts-related-list--grid4{
        gap:1em;
    }

    .posts-related-item-title {
        height: 3.5em;
        font-size: 1.1em;
    }
}

@media (max-width: 991px){
    .footer-content-text,.footer-policy-list,.footer-policy-list li +li::before{
        color: var(--color-white);
    }
    .footer-content-text{
        font-size: clamp(5px, 1.35vw, 12px);
        text-align: center;
    }
    .footer-policy-list{
        font-size: clamp(8px, 1.8vw, 12px);
    }
}

@media (max-width: 1099px){
    .fast-action-controls {
        display: none;
    }    

    .footer-hpv{
        padding: 2em 2em 80px 2em;
    }
}

/* Reponsive Section Question MOH */
@media (max-width: 575px) {
    .section-question-moh {
        font-size: 1.45vw;
        margin: 0 auto 2svh;
        padding: 0;
    }
    .section-question-moh.section-2 {
        padding: 0;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        /* aspect-ratio: 416 / 442; */
        width: 100%;
        margin: 0 auto;
        border-radius: 20px;

    }

    .group-question__left {
        font-size: 0.85em;
        width: 100%;
        height: 28svh;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        gap: 2svh;
        padding-top: 5svh;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: -15%;

    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 1em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 3svh;
        padding-top: 7svh;
    }

    .group-question__right {
        position: relative;
        width: 100%;
        height: 40svh;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .group-question-bg picture {
        height: inherit;
        object-fit: cover;
        width: 100%;
    }

    .group-tick {
        font-size: 1.1em;
    }

    .line-tick .line-moh {
        width: 80em;
        aspect-ratio: 1676 / 438;
        left: -5em;
        position: relative;
        top: -7em;
    }

    .line-tick .hand-img {
        position: relative;
        top: -4em;
        left: -6em;
        width: 20em;
        aspect-ratio: 290 / 188;
    }

    .group-question__content {
        gap: 5svh;
        top: 45%;
    }

    .group-question__btn {
        margin-top: 0;
        font-size: 1.2em;
    }
}

@media (max-width: 374px) {
    .section-question-moh {
        font-size: 0.6rem !important;
    }

    .line-tick .hand-img {
        position: relative;
        top: -2em;
        left: -17em;
        width: 22em;
        aspect-ratio: 290 / 188;
    }

    .group-tick {
        font-size: 1.1em;
    }
}

@media (min-width: 575px) and (max-width: 769px) {

    .group-question__content {
        gap: 5svh;
        font-size: 1.2em;
    }

    .bottom-kv-moh {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
    }

    .tagline-moh {
        width: 83.2em;
        margin-top: -0;
        aspect-ratio: 2144 / 868;
    }

    .section-question-moh {
        font-size: 1.15vw;
    }

    .group-question {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 90%;
        margin: 0 auto;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        font-size: 1em;
        padding-top: 10svh;
    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        font-size: 1em;
    }
    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: 3em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }
    .line-tick .hand-img {
        position: relative;
        top: -3.5em;
        width: 26em;
        left: -24em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
    }

    .group-question__content {
        gap: 5svh;
    }
}

@media only screen and (min-width: 769px) and (max-width: 992px) {
    .section-question-moh {
        font-size: 1rem;
    }

    .group-question {
        display: flex;
        flex-direction:column-reverse;
        height: auto;
        aspect-ratio: 416 / 442;
        width: 80%;
        margin: 0 auto;
    }

    .group-question__disc {
        font-weight: 500;
        line-height: 1.1;
        color: var(--neutral-900);
        text-align: center;
    }

    .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }
    .section-5 .group-question__left {
        width: 100%;
        padding: 2em;
        background: linear-gradient(0, #009885 0%, #3ABAA8 80%, rgba(74, 194, 179, 0) 99%);
        justify-content: center;
        gap: 5svh;
        padding-top: 10svh;
    }

    .group-question__right {
        position: relative;
        margin-bottom: -14%;
    }

    .group-tick {
        margin-top: 3em;
        font-size: 0.9em;
    }
    .line-tick .line-moh {
        width: 114em;
        aspect-ratio: 1986 / 464;
        left: -1em;
        position: relative;
        top: -11em;
        clip-path: inset(0 100% 0 0);
    }
    .line-tick .hand-img {
        position: relative;
        top: -4.5em;
        width: 29em;
        left: -20em;
        aspect-ratio: 290 / 188;
        opacity: 0;
        transition: opacity 0.5s ease -in-out 0.8s;
    }

    .line-tick {
        width: max-content;
        font-size: 0.8em;
    }

    .group-question__content {
        gap: 5svh;
    }
}

@media only screen and (min-width: 991px) and (max-width: 1199px) {
    .section-question-moh {
        font-size: 0.45vw;
    }
    .group-question__left {
        width: 65%;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
    .section-question-moh {
        font-size: 0.37vw;
    }
    .group-question__left {
        width: 60%;
    }
}

@media only screen and (min-width: 1400px) and (max-width: 1599px) {
    .section-question-moh {
        font-size: 0.35vw;
    }
    .group-question__left {
        width: 61%;
    }
}
/* END - Reponsive Section */