package frontend

import (
	"goweb/frontend/composers"

	"github.com/gofiber/fiber/v2"
	"github.com/networld-solution/sctx"
)

func SetupRoutes(app *fiber.App, serviceCtx sctx.ServiceContext) {
	homeCom := composers.ComposerHomeService(serviceCtx)
	productDetailCom := composers.ComposerProductDetailService(serviceCtx)
	cartCom := composers.ComposerCartServive(serviceCtx)
	productCom := composers.ComposerProductServive(serviceCtx)
	dealsCom := composers.ComposerDealsService(serviceCtx)
	blogsCom := composers.ComposerBlogsService(serviceCtx)
	newsDetailCom := composers.ComposerNewsDetailService(serviceCtx)
	privacyCom := composers.ComposerPrivacyService(serviceCtx)
	termsCom := composers.ComposerTermsService(serviceCtx)
	aboutCom := composers.ComposerAboutService(serviceCtx)
	contactCom := composers.ComposerContactService(serviceCtx)

	checkoutCom := composers.ComposerCheckoutService(serviceCtx)
	foryouCom := composers.ComposerForYouService(serviceCtx)
	trendsCom := composers.ComposerTrendsService(serviceCtx)

	app.Get("/", homeCom.HomeHdl())
	app.Get("/:slug.html", productDetailCom.ProductDetailHdl())
	app.Get("/gio-hang", cartCom.CartHdl())
	app.Get("/san-pham", productCom.ProductsHdl())
	app.Get("/thanh-toan", checkoutCom.CheckoutHdl())
	app.Get("/danh-cho-ban", foryouCom.ForYouHdl())
	app.Get("/xu-huong", trendsCom.TrendsHdl())
	app.Get("/uu-dai", dealsCom.DealsHdl())
	app.Get("/tin-tuc", blogsCom.BlogsHdl())
	app.Get("/tin-tuc/phong-cach-minimalist.html", newsDetailCom.NewsDetailHdl())
	app.Get("/chinh-sach-bao-mat", privacyCom.PrivacyHdl())
	app.Get("/dieu-kien-dieu-khoan", termsCom.TermsHdl())
	app.Get("/gioi-thieu", aboutCom.AboutHdl())
	app.Get("/lien-he", contactCom.ContactHdl())
}
