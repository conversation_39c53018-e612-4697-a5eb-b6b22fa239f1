package news

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/news/components"
	"goweb/views/layouts"
)

templ NewsDetail() {
	@layouts.Master(nil, &[]templ.Component{headNewsDetail()}, scriptNewsDetail()) {
		@components.NewsDetailBackground()
		@components.NewsDetailContent()
	}
}

templ headNewsDetail() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/news-detail.css") } />
}

templ scriptNewsDetail() {
	<script type="text/javascript" src={ templates.AssetURL("/static/js/news-detail.js") }></script>
}