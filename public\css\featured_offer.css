.collection-slides{
    padding: 0 1.37em;
    font-size: 1em;
    overflow: hidden;
    margin-top: 0.3em;
}

.collection__box {
    position: relative;
    overflow: hidden;
}
.collection__bg--secondary-light{
    background-color: var(--secondary-light);
    height: 100%;
}
.collection__bg--color-body{
    background-color: var(--color-body);
    height: 100%;
}
.collection__bg--dusty-rose{
    background-color: rgb(var(--rgb-dusty-rose));
    height: 100%;
}
.collection__box .collection__box__img {
    width: 50%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: 50%;
    display: flex;
    justify-content: center;
}
.collection__box .collection__box__full-img {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%
}
.collection__box .collection__box__full-img > img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.collection__box .product-image {
    text-align: center;
    height: 100%;
    transition: all .5s
}

.collection__box .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.collection__box .collection__content {
    bottom: 0;
    display: flex;
    align-items: end;
    justify-content: space-between;
    position: absolute;
    width: 100%;
    right: 0;
    padding: 3.42em;
    z-index: 1
}

.collection__box.collection__style .collection__content.full-img {
    align-items: center;
    justify-content: flex-end;
    padding: 0;
}
.collection__box.collection__style .collection__content {
    display: flex;
    align-items: end;
    justify-content: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    padding: 2.6em 3.45em;
    flex-direction: column;
    border-radius: 2em;
    z-index: 1
}

.collection__box.collection__style .collection__content .content__main__offer {
    padding: .35em 1.3em;
    background: var(--white);
    font-size: 1.37em;
    font-weight: 800;
    color: var(--title);
    margin-bottom: 1em;
    display: inline-block;
    text-transform: uppercase;
    width: max-content;
    line-height: 1.5;
}

.collection__sub {
    font-weight: 400;
    text-transform: uppercase;
}
.collection__sub-title{
    line-height: 1.5;
    letter-spacing: .2em;
    font-weight: 600;
    font-size: 2em;
}

.collection__content .sub-title2 {
    font-size: 3em;
    font-weight: 400;
    text-transform: uppercase;
    line-height: 1.5
}

.collection__content .bg-title {
    font-size: 8.13em;
    font-weight: 600;
    text-transform: uppercase;
    line-height: 1;
    display: block;
    color: var(--title);
    margin-bottom: 1.28em;
}

.collection__box.collection__style .collection__content .year {
    font-size: 4em;
    font-weight: 700;
    color: var(--title);
    display: block;
    line-height: 1;
    margin-bottom: .5em;
}
.collection__content .collection__content__main{
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.collection__content.full-img .collection__content__main{
    justify-content: flex-end;
    margin-bottom: 6em;
}
.collection__box.collection__style {
    height: 35em;
    border-radius: 1.75em;
}
.collection__content__btn, 
.collection__content__btn-full{
    position: relative;
    overflow: hidden;
    padding: 1.3em 3em;
    border-radius: 3em;
    border-width: .3em;
    z-index: 1;
    border: .3em solid var(--black);
    width: max-content;
    box-shadow: 0 0 5em 0 rgba(var(--rgb-black),.1);
}
.collection__content__btn:hover, 
.collection__content__btn-full:hover{
    color: var(--white);
    background-color: var(--black);
}
.collection__content__btn::after, 
.collection__content__btn-full::after{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.8;
    visibility: hidden;
    transform: translateX(-100%);
    border-right: 0.2em solid var(--white);
    background-color: rgba(255, 255, 255, .5);
    transition: all 0.5s ease;
    border-radius: 2em;
    color: var(--white);
    pointer-events: none;
    border-radius: 3em;
}
.collection__content__btn:hover:after, 
.collection__content__btn-full:hover::after{
    transform: translate(0);
    opacity: 0;
    visibility: visible;
    width: 100%;
    height: 100%;
}
.collection__content__btn span, 
.collection__content__btn-full span{
    font-size: 1.4em;
    font-weight: 500;
}
.swiper-button-next.collection__next, 
.swiper-button-prev.collection__prev{
    width: 3.5em;
    height: 3.5em;
    border-radius: 50%;
    background-color: rgba(var(--rgb-black), .2);
    color: var(--white);
}
.swiper-button-next.collection__next:after, 
.swiper-button-prev.collection__prev:after{
    font-size: 2em;
    color: rgb(var(--rgb-white));
    content: none;
}
.swiper-button-prev.collection__prev {
    transform: rotate(180deg);
}

@media (max-width: 575px){
    .collection-slides .swiper-slide {
        width: 100% ;
    }
}