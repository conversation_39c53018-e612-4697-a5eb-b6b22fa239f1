let player;

function onYouTubeIframeAPIReady() {
  player = new YT.Player("player", {
    videoId: "hPXejO5UTNk",
    events: {
      onStateChange: onPlayerStateChange,
    },
    playerVars: {
      autoplay: 0,
      mute: 1,
      rel: 0,
      playsinline: 1,
    },
  });
}

function onPlayerStateChange(event) {
  const videoPlay = document.querySelector(".video__play");
  const playerContainer = document.getElementById("player-container");
  switch (event.data) {
    case YT.PlayerState.PLAYING:
      playerContainer.style.opacity = "1";
      playerContainer.style.transition = "all 0.5s ease";
      playerContainer.style.visibility = "visible";
      videoPlay.style.display = "none";
      break;
    case YT.PlayerState.PAUSED:
      playerContainer.style.opacity = "0";
      playerContainer.style.transition = "all 0.5s ease";
      playerContainer.style.visibility = "hidden";
      videoPlay.style.display = "block";

      break;
    case YT.PlayerState.ENDED:
      playerContainer.style.opacity = "0";
      playerContainer.style.transition = "all 0.5s ease";
      playerContainer.style.visibility = "hidden";
      videoPlay.style.display = "block";
      break;
  }
}

document
  .getElementById("play-toggle-btn")
  .addEventListener("click", function () {
    const videoPlay = document.querySelector(".video__play");
    const state = player.getPlayerState();
    if (state === YT.PlayerState.PLAYING) {
      player.pauseVideo();
    } else {
      player.playVideo();
    }
    videoPlay.style.display = "none";
  });

document.addEventListener("click", function (event) {
  const playerContainer = document.getElementById("player-container");
  const playToggleButton = document.getElementById("play-toggle-btn");
  if (
    !playerContainer.contains(event.target) &&
    !playToggleButton.contains(event.target)
  ) {
    if (player && typeof player.pauseVideo === "function") {
      const state = player.getPlayerState();
      if (state === YT.PlayerState.PLAYING) {
        player.pauseVideo();
      }
    }
  }
});
