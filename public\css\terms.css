/* Terms & Conditions Styles */

/* Background Section */
.terms-bg {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 25em;
    display: flex;
    align-items: center;
    justify-content: center;
}

.terms-bg--overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.terms-bg .container {
    position: relative;
    z-index: 2;
    text-align: center;
}

.terms-bg .content {
    color: var(--white);
}

.terms-bg .title {
    font-size: 3.2em;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Breadcrumb */
.terms-breadcrumb {
    background: var(--white);
    padding: 1.5em 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.652vw;
}

.terms-breadcrumb .list {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5em;
}

.terms-breadcrumb .item {
    display: flex;
    align-items: center;
    font-size: 1.4em;
    color: var(--text-muted);
}

.terms-breadcrumb .item:not(:last-child)::after {
    content: '';
    width: 1.2em;
    height: 1.2em;
    margin-left: 0.5em;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='m9 18 6-6-6-6'/%3E%3C/svg%3E") no-repeat center;
    background-size: contain;
}

.terms-breadcrumb .link {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

.terms-breadcrumb .link:hover {
    color: var(--primary-hover);
}

/* Terms Policy Content */
.terms-policy {
    padding: 4em 0;
    background: var(--white);
    font-size: 0.652vw;
}

.terms-policy__content {
    margin: 0 auto;
}

.terms-policy__title {
    font-size: 3.2em;
    font-weight: 700;
    color: var(--text-dark);
    text-align: center;
    margin: 0 0 1.5em 0;
    line-height: 1.2;
}

/* Terms Section */
.terms-section {
    margin-bottom: 4em;
    display: flex;
    align-items: flex-start;
    gap: 2em;
}

/* Header with number and title on same line */
.terms-section .terms__header {
    display: flex;
    align-items: flex-start;
    gap: 1.5em;
    margin-bottom: 2em;
    flex: 3;
}

.terms__number {
    flex-shrink: 0;
    background: var(--primary-color);
    color: #1B46AF;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4.8em;
    font-weight: 800;
    flex: 2;
    line-height: 1;
}

.terms__title {
    font-size: 2.4em;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
    line-height: 1.3;
    flex: 8;
}

.terms__text {
    flex: 7;
    font-size: 1.6em;
    line-height: 1.6;
    color: var(--text-color);
}

.terms__text p {
    margin: 0 0 1.5em 0;
}

.terms__text p:last-child {
    margin-bottom: 0;
}

/* Terms Lists */
.terms-list {
    list-style: none;
    padding: 0;
    margin: 1.5em 0;
}

.terms-list li {
    position: relative;
    padding-left: 2em;
    margin-bottom: 1em;
    line-height: 1.6;
}

.terms-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2em;
}

.terms-contact {
    list-style: none;
    padding: 0;
}

.terms-contact li {
    margin-bottom: 0.8em;
    line-height: 1.5;
}

.terms-contact a {
    color: var(--primary-color);
    text-decoration: none;
}

.terms-contact a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .terms-bg {
        min-height: 20em;
    }

    .terms-bg .title {
        font-size: 2.4em;
    }

    .terms-breadcrumb .item {
        font-size: 1.2em;
    }

    .terms-policy {
        padding: 2em 0;
        font-size: 1.25vw;
    }

    .terms-policy__title {
        font-size: 2.4em;
        margin-bottom: 2em;
    }

    .terms-section {
        margin-bottom: 3em;
        display: flex;
        flex-direction: column;
        gap: 1.5em;
    }

    .terms-section .terms__header {
        display: flex;
        align-items: center;
        gap: 1.5em;
        margin-bottom: 0;
    }

    .terms__number {
        font-size: 3.2em;
        font-weight: 800;
        line-height: 1;
        flex-shrink: 0;
        flex: none;
    }

    .terms__title {
        font-size: 1.8em;
        margin: 0;
    }

    .terms__text {
        font-size: 1.3em;
    }

    .terms-contact li {
        font-size: 1.4em;
    }
}

@media (max-width: 575.98px) {
    .terms-policy {
        padding: 2em 0;
        font-size: 2vw;
    }

    .terms-bg .title {
        font-size: 2em;
    }

    .terms-policy__title {
        font-size: 2em;
    }

    .terms-section {
        gap: 1.2em;
        margin-bottom: 2.5em;
    }

    .terms-section .terms__header {
        gap: 1.2em;
    }

    .terms__number {
        font-size: 2.8em;
    }

    .terms__title {
        font-size: 1.6em;
    }

    .terms__text {
        font-size: 1.2em;
    }
}