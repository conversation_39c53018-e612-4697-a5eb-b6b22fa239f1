package components

import (
    "goweb/views/partials"
)

templ RelatedProduct(){
    <div class="related-product__wrapper">
        <div class="related-product__header">
            <div class="sec-head">
                <h2 class="sec-head__title">Sản phẩm liên quan</h2>
                <div class="sec-head__btn">
                    <a href="#" class="btn-primary">
                        <span>Xem tất cả</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="swiper related-product__swiper">
            <div class="swiper-wrapper">
                {{z:=1}}
                for i:=1;i<=5; i++ {
                    @partials.ProductItem(" swiper-slide")
                    {{z = z+3}}
                }
            </div>

            <div class="swiper-button-next related-product__next"></div>
            <div class="swiper-button-prev related-product__prev"></div>
        </div>
    </div>
    <div class="related-product__wrapper">
        <div class="related-product__header">
            <div class="sec-head">
                <h2 class="sec-head__title">Sản phẩm cùng loại</h2>
                <div class="sec-head__btn">
                    <a href="#" class="btn-primary">
                        <span>Xem tất cả</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="swiper related-product__swiper">
            <div class="swiper-wrapper">
                {{k:=1}}
                for i:=1;i<=5; i++ {
                    @partials.ProductItem(" swiper-slide")
                    {{k = k+3}}
                }
            </div>

            <div class="swiper-button-next related-product__next"></div>
            <div class="swiper-button-prev related-product__prev"></div>
        </div>
    </div>
    
}