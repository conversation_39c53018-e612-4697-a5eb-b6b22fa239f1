const product_detail = (function () {
  const selectedFiles = [];
  const selectedFileKeys = new Set();
  const maxTotal = 6;
  const previewContainer = document.getElementById("file_preview");
  let idCounter = 0;

  const swiperProductDetail = new Swiper(".swiper-product-detail-img-main", {
    slidesPerView: 1,
    spaceBetween: calcSpaceBetweenFromVW(1.5),
    speed: 0,
    preloadImages: true,
    lazy: false,
  });
  new Swiper(".related-product__swiper", {
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: true,
    },
    navigation: {
      nextEl: ".related-product__next",
      prevEl: ".related-product__prev",
    },
    spaceBetween: calcSpaceBetweenFromVW(1.5),
    breakpoints: {
      0: {
        slidesPerView: 2,
      },
      769: {
        slidesPerView: 5,
      },
    },
  });
  function calcSpaceBetweenFromVW(em) {
    const fs = (window.innerWidth * 0.652) / 100;
    if (fs > 20) return 20;
    return em * fs;
  }
  document
    .querySelectorAll(".product-thumbnails >img")
    .forEach((thumb, index) => {
      thumb.addEventListener("click", () => {
        swiperProductDetail.slideTo(index);
        // Toggle active class
        document
          .querySelectorAll(".product-thumbnails >img")
          .forEach((img) => img.classList.remove("active"));
        thumb.classList.add("active");
      });
    });

  // Optional: set active on initial load
  document.querySelectorAll(".comma-separated").forEach((container) => {
    const paragraphs = container.querySelectorAll("p");

    paragraphs.forEach((p) => {
      const items = p.querySelectorAll(".comma-item");
      items.forEach((item, index) => {
        const next = item.nextSibling;
        if (
          next &&
          next.nodeType === Node.TEXT_NODE &&
          next.textContent.trim() === ","
        ) {
          next.remove();
        }
        if (index < items.length - 1) {
          item.insertAdjacentText("afterend", ", ");
        }
      });
    });
  });

  const initOffcanvasSupport = () => {
    const offcanvas = document.getElementById("product-offcanvas");
    if (!offcanvas) return;

    const content = document.getElementById("offcanvas-content");
    const overlay = offcanvas.querySelector(".offcanvas__overlay");
    const closeBtn = offcanvas.querySelector(".offcanvas__close");

    const showOffcanvasFromTemplate = (templateId) => {
      const template = document.getElementById(templateId);
      if (!template) return;

      content.innerHTML = template.innerHTML;
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
      document.body.style.paddingRight = `${scrollbarWidth}px`;
      offcanvas.classList.add("active");
      document.body.classList.add("offcanvas-open");
    };

    const hideOffcanvas = () => {
      offcanvas.classList.remove("active");
      content.innerHTML = "";
      document.body.style.paddingRight = '';
      document.body.classList.remove("offcanvas-open");
    };

    document.querySelector(".product-detail__return-policy")?.addEventListener("click", () => {
      showOffcanvasFromTemplate("offcanvas-content-return");
    });

    document.querySelector(".product-detail__size-guide")?.addEventListener("click", () => {
      showOffcanvasFromTemplate("offcanvas-content-size");
    });

    overlay?.addEventListener("click", hideOffcanvas);
    closeBtn?.addEventListener("click", hideOffcanvas);
  };

  const changePruductColor = () => {
    document.querySelectorAll(".product").forEach((product) => {
      product.addEventListener("click", (e) => {
        const color = e.target.closest(".product-color");
        if (!color || color.classList.contains("product-color--more")) return;

        // Skip if this color is inside a gift product
        if (color.closest('.gift-product')) {
          return;
        }

        const img = color.getAttribute("data-img");
        const productWrapper = color.closest(".product");
        const activeSlide = productWrapper.querySelector(
          ".swiper-slide-active"
        );
        let mainImg = "";
        if (activeSlide) {
          mainImg = activeSlide.querySelector(".product__img");
        } else {
          mainImg = productWrapper.querySelector(".product__img");
        }
        const allColors = [
          ...productWrapper.querySelectorAll(".product-color"),
        ].filter((el) => el.closest(".product") === productWrapper);
        allColors.forEach((c) => c.classList.remove("active"));
        color.classList.add("active");
        if (mainImg && img) {
          mainImg.setAttribute("src", img);
        }
      });
    });
  };

  // Size selection handler for main product with color filtering
  const initMainProductSizeSelection = () => {
    const mainProductContainer = document.querySelector('.product-detail__half-layout.product');
    if (!mainProductContainer) return;

    const sizeItems = mainProductContainer.querySelectorAll('.product-size__item');
    sizeItems.forEach(item => {
      item.addEventListener('click', function () {
        // Remove active class from all size items in main product
        sizeItems.forEach(sizeItem => {
          sizeItem.classList.remove('active');
        });

        // Add active class to clicked item
        this.classList.add('active');

        // Handle color filtering based on size selection
        if (this.dataset.color) {
          try {
            const colorItems = JSON.parse(this.dataset.color);
            const productColors = mainProductContainer.querySelector('.product-colors');

            if (productColors && Array.isArray(colorItems)) {
              // Get current active color to preserve selection if possible
              const currentActiveColor = productColors.querySelector('.product-color.active');
              const currentImg = currentActiveColor?.getAttribute('data-img');

              // Clear existing colors
              productColors.innerHTML = '';

              // Add new colors based on selected size
              let foundMatch = false;
              colorItems.forEach((colorData, index) => {
                const colorElement = document.createElement('li');
                colorElement.className = 'product-color';
                colorElement.setAttribute('data-img', colorData.img);

                const colorSpan = document.createElement('span');
                colorSpan.className = 'product-color__item';
                colorSpan.style.backgroundColor = colorData.color;

                colorElement.appendChild(colorSpan);
                productColors.appendChild(colorElement);

                // Check if this matches the previously selected color
                if (currentImg && colorData.img === currentImg) {
                  colorElement.classList.add('active');
                  foundMatch = true;
                } else if (index === 0 && !foundMatch) {
                  // Set first color as active if no match found
                  colorElement.classList.add('active');
                }
              });

              // Update main image based on active color
              const activeColor = productColors.querySelector('.product-color.active');
              if (activeColor) {
                const newImg = activeColor.getAttribute('data-img');
                const mainImg = mainProductContainer.querySelector('.product__img');
                if (mainImg && newImg) {
                  mainImg.setAttribute('src', newImg);
                }
              }
            }
          } catch (error) {
            console.error('Error parsing color data for main product:', error);
          }
        }
      });
    });
  };

  // Size selection handler for addon-deals product with color filtering
  const initAddonProductSizeSelection = () => {
    const addonProductContainer = document.querySelector('.addon-deals__content-wrapper.product');
    if (!addonProductContainer) return;

    const sizeItems = addonProductContainer.querySelectorAll('.product-size__item');
    sizeItems.forEach(item => {
      item.addEventListener('click', function () {
        // Remove active class from all size items in addon product
        sizeItems.forEach(sizeItem => {
          sizeItem.classList.remove('active');
        });

        // Add active class to clicked item
        this.classList.add('active');

        // Handle color filtering based on size selection
        if (this.dataset.color) {
          try {
            const colorItems = JSON.parse(this.dataset.color);
            const productColors = addonProductContainer.querySelector('.product-colors');

            if (productColors && Array.isArray(colorItems)) {
              // Get current active color to preserve selection if possible
              const currentActiveColor = productColors.querySelector('.product-color.active');
              const currentImg = currentActiveColor?.getAttribute('data-img');

              // Clear existing colors
              productColors.innerHTML = '';

              // Add new colors based on selected size
              let foundMatch = false;
              colorItems.forEach((colorData, index) => {
                const colorElement = document.createElement('li');
                colorElement.className = 'product-color';
                colorElement.setAttribute('data-img', colorData.img);

                const colorSpan = document.createElement('span');
                colorSpan.className = 'product-color__item';
                colorSpan.style.backgroundColor = colorData.color;

                colorElement.appendChild(colorSpan);
                productColors.appendChild(colorElement);

                // Check if this matches the previously selected color
                if (currentImg && colorData.img === currentImg) {
                  colorElement.classList.add('active');
                  foundMatch = true;
                } else if (index === 0 && !foundMatch) {
                  // Set first color as active if no match found
                  colorElement.classList.add('active');
                }
              });

              // Update addon product image based on active color
              const activeColor = productColors.querySelector('.product-color.active');
              if (activeColor) {
                const newImg = activeColor.getAttribute('data-img');
                const addonImg = addonProductContainer.querySelector('.product__img');
                if (addonImg && newImg) {
                  addonImg.setAttribute('src', newImg);
                }
              }
            }
          } catch (error) {
            console.error('Error parsing color data for addon product:', error);
          }
        }
      });
    });
  };

  //process upload img/video
  function bytesToMB(bytes) {
    return bytes / (1024 * 1024);
  }
  function createFileId() {
    return `file-${idCounter++}`;
  }
  function addFiles(newFiles) {
    const imageCount = selectedFiles.filter((f) =>
      f.file.type.startsWith("image/")
    ).length;
    const videoCount = selectedFiles.filter((f) =>
      f.file.type.startsWith("video/")
    ).length;

    let newImageCount = 0;
    let newVideoCount = 0;
    let hasNewFile = false;
    let duplicateFiles = [];
    let errorMessages = [];
    const validFiles = [];

    for (let i = 0; i < newFiles.length; i++) {
      const file = newFiles[i];
      const fileKey = file.name + "_" + file.lastModified;

      if (selectedFileKeys.has(fileKey)) {
        duplicateFiles.push(file.name);
        continue;
      }

      const isImage = file.type.startsWith("image/");
      const isVideo = file.type.startsWith("video/");

      if (isVideo) {
        if (bytesToMB(file.size) > 60) {
          errorMessages.push(`Video "${file.name}" vượt quá giới hạn 60MB.`);
          continue;
        }
        if (videoCount + newVideoCount >= 1) {
          errorMessages.push(`Đã đủ 1 video. "${file.name}" bị bỏ qua.`);
          continue;
        }
        newVideoCount++;
      }

      if (isImage) {
        if (imageCount + newImageCount >= 5) {
          errorMessages.push("Chỉ được phép tải tối đa 5 ảnh.");
          break;
        }
        newImageCount++;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      if (duplicateFiles.length > 0) {
        errorMessages.push(
          `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
        );
      }
      if (errorMessages.length > 0) {
        alert(errorMessages.join("\n"));
      }
      return;
    }

    // Kiểm tra tổng giới hạn file
    const totalCount = selectedFiles.length + validFiles.length;
    if (totalCount > 6) {
      errorMessages.push("Tổng cộng chỉ được phép tải tối đa 6 tệp (5 ảnh + 1 video).");
      if (duplicateFiles.length > 0) {
        errorMessages.push(
          `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
        );
      }
      alert(errorMessages.join("\n"));
      return;
    }

    // Thêm vào danh sách
    for (const file of validFiles) {
      const fileKey = file.name + "_" + file.lastModified;
      const id = createFileId();
      selectedFiles.push({ file, id, fileKey });
      selectedFileKeys.add(fileKey);
      hasNewFile = true;
    }

    if (duplicateFiles.length > 0) {
      errorMessages.push(
        `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
      );
    }

    if (errorMessages.length > 0) {
      alert(errorMessages.join("\n"));
    }

    if (hasNewFile) {
      renderAllPreviews();
    }
  }

  function renderAllPreviews() {
    previewContainer.innerHTML = "";
    const images = selectedFiles.filter((f) =>
      f.file.type.startsWith("image/")
    );
    const videos = selectedFiles.filter((f) =>
      f.file.type.startsWith("video/")
    );

    [...images, ...videos].forEach(({ file, id }) => {
      renderSinglePreview(
        file,
        id,
        file.type.startsWith("image/") ? "image" : "video"
      );
    });
  }
  function renderSinglePreview(file, id, type) {
    const reader = new FileReader();

    reader.onload = function (e) {
      let el;
      if (type === "image") {
        el = document.createElement("img");
        el.classList.add("preview-thumb");
        el.src = e.target.result;
      } else if (type === "video") {
        el = document.createElement("video");
        el.classList.add("preview-thumb");
        el.src = e.target.result;
        el.controls = true;
      }
      const wrapper = document.createElement("div");
      wrapper.classList.add("preview-wrapper");
      wrapper.setAttribute("data-id", id);
      const removeBtn = document.createElement("button");
      removeBtn.innerHTML =
        '<svg class="cart-icon__delete"><use href="#icon-close"></use></svg>';
      removeBtn.classList.add("preview-remove");
      removeBtn.addEventListener("click", () => {
        const index = selectedFiles.findIndex((f) => f.id === id);
        if (index !== -1) {
          const fileKey = selectedFiles[index].fileKey;
          selectedFiles.splice(index, 1);
          selectedFileKeys.delete(fileKey);
        }
        wrapper.remove();
      });
      wrapper.appendChild(el);
      wrapper.appendChild(removeBtn);
      previewContainer.appendChild(wrapper);
    };
    reader.readAsDataURL(file);
  }
  function handleImageInput(e) {
    addFiles(e.target.files, "image");
    e.target.value = "";
  }
  function handleVideoInput(e) {
    addFiles(e.target.files, "video");
    e.target.value = "";
  }
  function initCommentFileUploadPreview() {
    const imageInput = document.getElementById("upload_image");
    const videoInput = document.getElementById("upload_video");

    if (imageInput) {
      imageInput.addEventListener("change", handleImageInput);
    }

    if (videoInput) {
      videoInput.addEventListener("change", handleVideoInput);
    }
  }
  initCommentFileUploadPreview();

  function syncHeights() {
    const left = document.querySelector(".product-detail__content-left");
    const right = document.querySelector(".product-detail__content-right");
    if (!left || !right) return;
    if (document.documentElement.clientWidth <= 768) {
      left.style.minHeight = "0";
      return;
    }
    left.style.minHeight = "0";
    const rightHeight = right.getBoundingClientRect().height;
    const rightWidth = right.getBoundingClientRect().width;
    const paddingBottom = rightWidth * 0.05;

    const reducedHeight = Math.ceil(rightHeight - paddingBottom);
    left.style.minHeight = `${reducedHeight}px`;
  }

  function initStickyHeightSync() {
    syncHeights();
    window.addEventListener("resize", syncHeights);
    const rightEl = document.querySelector(".product-detail__content-right");
    if (rightEl) {
      const observer = new MutationObserver(syncHeights);
      observer.observe(rightEl, { childList: true, subtree: true });
    }
  }

  const initTabSwitcher = () => {
    const tabButtons = document.querySelectorAll(
      ".nav-product-detail .nav-link"
    );
    const tabPanes = document.querySelectorAll(".tab-pane");

    tabButtons.forEach((btn, index) => {
      btn.addEventListener("click", () => {
        tabButtons.forEach((b) => {
          b.classList.remove("active");
          b.setAttribute("aria-selected", "false");
          b.setAttribute("tabindex", "-1");
        });

        tabPanes.forEach((pane) => {
          pane.classList.remove("active");
          pane.setAttribute("hidden", "true");
        });

        btn.classList.add("active");
        btn.setAttribute("aria-selected", "true");
        btn.setAttribute("tabindex", "0");

        const target = index === 0 ? "description" : "reviews";
        const targetPane = document.querySelector(
          `.tab-pane[data-tab="${target}"]`
        );

        if (targetPane) {
          targetPane.classList.add("active");
          targetPane.removeAttribute("hidden");
        }
        syncHeights();
      });
    });
  };

  const initRatingClick = () => {
    const ratingElement = document.querySelector(".product-rating");
    const tabButtons = document.querySelectorAll(
      ".nav-product-detail .nav-link"
    );
    const tabPanes = document.querySelectorAll(".tab-pane");
    const tabWrapper = document.querySelector(".nav-tabs.nav-product-detail");

    if (!ratingElement || tabButtons.length < 2 || !tabWrapper) return;

    ratingElement.addEventListener("click", () => {
      tabWrapper.scrollIntoView({ behavior: "smooth", block: "start" });

      tabButtons.forEach((b) => b.classList.remove("active"));
      tabPanes.forEach((p) => p.classList.remove("active"));

      tabButtons[1].classList.add("active");
      const reviewPane = document.querySelector(
        '.tab-pane[data-tab="reviews"]'
      );
      reviewPane?.classList.add("active");

      syncHeights();
    });
  };

  // Helper function to format price
  const formatPrice = (price) => {
    const numPrice = parseInt(price);
    return numPrice.toLocaleString('vi-VN');
  };

  // Add combo functionality
  const addComboToCart = () => {
    // Get main product (the whole product detail page)
    const mainProductContainer = document.querySelector('.product-detail__half-layout.product');
    // Get addon product
    const addonProduct = document.querySelector('.addon-deals__content-wrapper.product');

    if (!mainProductContainer) {
      console.error('Main product not found');
      alert('Không tìm thấy sản phẩm chính. Vui lòng tải lại trang.');
      return;
    }

    if (!addonProduct) {
      console.error('Addon product not found');
      alert('Không tìm thấy sản phẩm khuyến mãi. Vui lòng tải lại trang.');
      return;
    }

    // Check if main product has selected size and color
    const mainSelectedSize = mainProductContainer.querySelector('.product-size__item.active');
    const mainSelectedColor = mainProductContainer.querySelector('.product-color.active');

    if (!mainSelectedSize) {
      alert('Vui lòng chọn kích thước cho sản phẩm chính.');
      return;
    }

    if (!mainSelectedColor) {
      alert('Vui lòng chọn màu sắc cho sản phẩm chính.');
      return;
    }

    // Check if addon product has selected size and color
    const addonSelectedSize = addonProduct.querySelector('.product-size__item.active');
    const addonSelectedColor = addonProduct.querySelector('.product-color.active');

    if (!addonSelectedSize) {
      alert('Vui lòng chọn kích thước cho sản phẩm khuyến mãi.');
      return;
    }

    if (!addonSelectedColor) {
      alert('Vui lòng chọn màu sắc cho sản phẩm khuyến mãi.');
      return;
    }

    // Extract main product data
    const mainProductData = extractMainProductData(mainProductContainer);
    // Extract addon product data
    const addonProductData = extractProductData(addonProduct, 'addon-001');

    if (!mainProductData || !addonProductData) {
      alert('Không thể lấy thông tin sản phẩm. Vui lòng thử lại.');
      return;
    }

    // Add products to cart using existing cart functionality
    if (typeof cartUpdater !== 'undefined' && cartUpdater.addToCartSmart) {
      try {
        // Add main product
        cartUpdater.addToCartSmart(mainProductData);
        // Add addon product
        cartUpdater.addToCartSmart(addonProductData);

        // Show success message
        if (cartUpdater.showGiftAddedMessage) {
          cartUpdater.showGiftAddedMessage('Đã thêm combo vào giỏ hàng!');
        }

        // Open cart sidebar
        const cartSidebarWrapper = document.querySelector('#cartSidebar');
        if (cartSidebarWrapper) {
          cartSidebarWrapper.classList.add('is-open');
        }
      } catch (error) {
        console.error('Error adding combo to cart:', error);
        alert('Có lỗi xảy ra khi thêm combo vào giỏ hàng. Vui lòng thử lại.');
      }
    } else {
      console.error('cartUpdater not available');
      alert('Chức năng giỏ hàng chưa sẵn sàng. Vui lòng tải lại trang.');
    }
  };

  // Helper function to extract main product data from main product container
  const extractMainProductData = (productContainer) => {
    try {
      // Get product ID from add to cart button
      const productId = productContainer.querySelector('.btn_add_to_cart')?.dataset.productId || 'main-product-001';

      // Get product name from title
      const nameEl = productContainer.querySelector('.product-detail__title');
      const name = nameEl ? nameEl.textContent.trim() : 'Sản phẩm chính';

      // Get product image from viewer
      const imgEl = productContainer.querySelector('.product__img');
      const image = imgEl ? imgEl.src : '';

      // Get product price
      const priceEl = productContainer.querySelector('.product-detail__price span:first-child');
      const price = priceEl ? priceEl.textContent.trim() : '0đ';

      // Get selected size only
      const activeSizeEl = productContainer.querySelector('.product-size__item.active');
      const selectedSize = activeSizeEl ? {
        text: activeSizeEl.textContent.trim(),
        isActive: true,
        dataColor: activeSizeEl.dataset.color || null
      } : { text: 'M', isActive: true, dataColor: null };

      // Get selected color only
      const activeColorEl = productContainer.querySelector('.product-color.active');
      const selectedColor = activeColorEl ? {
        backgroundColor: activeColorEl.querySelector('.product-color__item')?.style.backgroundColor || '#000000',
        dataImg: activeColorEl.dataset.img || image,
        isActive: true
      } : { backgroundColor: '#000000', dataImg: image, isActive: true };

      const sizes = [selectedSize];
      const colors = [selectedColor];

      // Get quantity
      const quantityInput = productContainer.querySelector('.quantity-input');
      const quantity = quantityInput ? parseInt(quantityInput.value || '1', 10) : 1;

      return {
        product_id: productId,
        name,
        image,
        sizes,
        colors,
        quantity,
        price
      };
    } catch (error) {
      console.error('Error extracting main product data:', error);
      return null;
    }
  };

  // Helper function to extract gift product data
  const extractGiftProductData = (giftEl) => {
    if (!giftEl) return null;

    try {
      // Extract data from data attributes (following existing gift logic)
      const availableSizes = JSON.parse(giftEl.dataset.giftSizes || '[]');
      const availableColors = JSON.parse(giftEl.dataset.giftColors || '[]');

      // Create gift product object with cart-compatible structure
      const giftProduct = {
        product_id: giftEl.dataset.giftId || 'gift-default',
        name: giftEl.dataset.giftName || 'Gift Product',
        image: giftEl.dataset.giftImage || '',
        price: (giftEl.dataset.giftPrice || '0') + (giftEl.dataset.giftCurrency || 'đ'),
        originalPrice: (giftEl.dataset.giftOriginalPrice || '0') + (giftEl.dataset.giftCurrency || 'đ'),
        quantity: 1,
        isGift: true,
        selectedSize: availableSizes[0] || 'M', // Default to first available size
        selectedColor: availableColors[0]?.color || '#000000',
        sizes: availableSizes.map((size, index) => ({
          text: size,
          isActive: index === 0, // First size is active
          dataColor: null // Gift products don't need complex color data
        })),
        colors: availableColors.map((color, index) => ({
          backgroundColor: color.color,
          dataImg: color.img,
          isActive: index === 0 // First color is active
        }))
      };

      return giftProduct;
    } catch (error) {
      console.error('Error extracting gift product data:', error);
      return null;
    }
  };

  // Helper function to extract product data from DOM element (for addon products)
  const extractProductData = (productEl, defaultProductId) => {
    try {
      // Get product ID
      const productId = productEl.querySelector('.btn_add_to_cart, .btn_add_to_cart_addon')?.dataset.productId || defaultProductId;

      // Get product name
      const nameEl = productEl.querySelector('.product-detail__title a, .addon-deals__top-title a');
      const name = nameEl ? nameEl.textContent.trim() : 'Sản phẩm';

      // Get product image
      const imgEl = productEl.querySelector('.product__img, .addon-deals__thumb img');
      const image = imgEl ? imgEl.src : '';

      // Get product price
      const priceEl = productEl.querySelector('.product-detail__price span:first-child, .addon-deals__price .product-detail__price span:first-child');
      const price = priceEl ? priceEl.textContent.trim() : '0đ';

      // Get selected size only
      const activeSizeEl = productEl.querySelector('.product-size__item.active');
      const selectedSize = activeSizeEl ? {
        text: activeSizeEl.textContent.trim(),
        isActive: true,
        dataColor: activeSizeEl.dataset.color || null
      } : { text: 'M', isActive: true, dataColor: null };

      // Get selected color only
      const activeColorEl = productEl.querySelector('.product-color.active');
      const selectedColor = activeColorEl ? {
        backgroundColor: activeColorEl.querySelector('.product-color__item')?.style.backgroundColor || '#000000',
        dataImg: activeColorEl.dataset.img || image,
        isActive: true
      } : { backgroundColor: '#000000', dataImg: image, isActive: true };

      const sizes = [selectedSize];
      const colors = [selectedColor];

      // Get quantity
      const quantityInput = productEl.querySelector('.quantity-input');
      const quantity = quantityInput ? parseInt(quantityInput.value || '1', 10) : 1;

      return {
        product_id: productId,
        name,
        image,
        sizes,
        colors,
        quantity,
        price
      };
    } catch (error) {
      console.error('Error extracting product data:', error);
      return null;
    }
  };

  // Add gift product to cart function
  const addGiftProductToCart = (selectedSize) => {

    // Get gift product data from DOM
    const giftContentElement = document.querySelector('.gift-product__content');
    if (!giftContentElement) {
      return;
    }

    // Extract data from data attributes with error handling
    try {
      const availableSizes = JSON.parse(giftContentElement.dataset.giftSizes || '[]');
      const availableColors = JSON.parse(giftContentElement.dataset.giftColors || '[]');

      // Create gift product object with cart-compatible structure
      const giftProduct = {
        id: giftContentElement.dataset.giftId || 'gift-default',
        product_id: giftContentElement.dataset.giftId || 'gift-default',
        name: giftContentElement.dataset.giftName || 'Gift Product',
        image: giftContentElement.dataset.giftImage || '',
        price: (giftContentElement.dataset.giftPrice || '0') + (giftContentElement.dataset.giftCurrency || 'đ'),
        originalPrice: formatPrice(giftContentElement.dataset.giftOriginalPrice || '0') + (giftContentElement.dataset.giftCurrency || 'đ'),
        condition: giftContentElement.dataset.giftCondition || '',
        sku: giftContentElement.dataset.giftSku || '',
        quantity: 1,
        isGift: true,
        selectedSize: selectedSize,
        selectedColor: availableColors[0]?.color || '#000000',
        availableSizes: availableSizes,
        availableColors: availableColors,
        sizes: availableSizes.map(size => ({
          text: size,
          isActive: selectedSize === size,
          dataColor: JSON.stringify(availableColors)
        })),
        colors: availableColors.map((color, index) => ({
          backgroundColor: color.color || '#000000',
          isActive: index === 0,
          dataImg: color.img || ''
        }))
      };

      // Check if cart containers exist
      const cartSidebarContainer = document.querySelector('#cartSidebar .cart__list');
      const mainCartContainer = document.querySelector('.products-cart');

      if (!cartSidebarContainer && !mainCartContainer) {
        return;
      }

      // Use cartUpdater from master.templ
      if (typeof cartUpdater !== 'undefined' && cartUpdater.addGiftProductToCart) {
        cartUpdater.addGiftProductToCart(giftProduct);
      }
    } catch (error) {
      console.error('Error adding gift product to cart:', error);
      if (typeof cartUpdater !== 'undefined' && cartUpdater.showGiftAddedMessage) {
        cartUpdater.showGiftAddedMessage('Có lỗi xảy ra khi thêm sản phẩm tặng!');
      }
    }
  };





  // Populate modal with gift data from DOM
  const populateModalWithGiftData = () => {
    const giftContentElement = document.querySelector('.gift-product__content');
    if (!giftContentElement) return;

    try {
      // Update modal elements with gift data
      const modalImage = document.getElementById('giftModalImage');
      const modalTitle = document.getElementById('giftModalTitle');
      const modalDescription = document.getElementById('giftModalDescription');

      if (modalImage) {
        modalImage.src = giftContentElement.dataset.giftImage || '';
        modalImage.alt = giftContentElement.dataset.giftName || 'Gift Product';
      }

      if (modalTitle) {
        modalTitle.textContent = giftContentElement.dataset.giftName || 'Gift Product';
      }

      if (modalDescription) {
        modalDescription.textContent = giftContentElement.dataset.giftCondition || '';
      }

      // Update modal title
      const modalTitleElement = document.querySelector('.gift-modal__title');
      if (modalTitleElement) {
        modalTitleElement.textContent = `Chọn size cho ${giftContentElement.dataset.giftName || 'sản phẩm tặng'}`;
      }
    } catch (error) {
      console.error('Error populating modal with gift data:', error);
    }
  };



  const initGiftSizeModal = () => {
    const giftSizeItems = document.querySelectorAll('.gift-size-list .product-size__item');
    const confirmBtn = document.getElementById('confirmGiftSize');
    const cancelBtn = document.getElementById('cancelGiftSize');
    const closeBtn = document.getElementById('closeGiftSizeModal');
    const openBtn = document.getElementById('openGiftSizeModal');
    const sizeBtn = document.querySelector('.gift-product__size-btn span');
    const modalElement = document.getElementById('giftSizeModal');
    let selectedSize = null;

    // Check for pre-selected size from HTML
    const preSelectedSize = document.querySelector('.gift-size-list .product-size__item.product-size--active');
    if (preSelectedSize) {
      selectedSize = preSelectedSize.dataset.size;
    }

    if (!modalElement) return;

    // Open modal
    const openModal = () => {
      // Populate modal with gift data
      populateModalWithGiftData();

      // Update selectedSize from current active state
      const currentActiveSize = document.querySelector('.gift-size-list .product-size__item.product-size--active');
      if (currentActiveSize) {
        selectedSize = currentActiveSize.dataset.size;
      }

      modalElement.classList.add('active');
      document.body.style.overflow = 'hidden';
    };

    // Close modal
    const closeModal = () => {
      modalElement.classList.remove('active');
      document.body.style.overflow = '';

      // Reset to default HTML state (size S active)
      giftSizeItems.forEach(i => i.classList.remove('product-size--active'));

      // Restore default active state from HTML
      const sizeS = document.querySelector('.gift-size-list .product-size__item[data-size="S"]');
      if (sizeS) {
        sizeS.classList.add('product-size--active');
        selectedSize = 'S';
      }
    };

    // Event listeners for open/close
    if (openBtn) {
      openBtn.addEventListener('click', openModal);
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', closeModal);
    }

    if (cancelBtn) {
      cancelBtn.addEventListener('click', closeModal);
    }

    // Close modal when clicking overlay
    modalElement.addEventListener('click', (e) => {
      if (e.target === modalElement) {
        closeModal();
      }
    });

    // Handle size selection
    giftSizeItems.forEach(item => {
      item.addEventListener('click', () => {
        // Remove active class from all items
        giftSizeItems.forEach(i => i.classList.remove('product-size--active'));

        // Add active class to clicked item
        item.classList.add('product-size--active');

        // Store selected size
        selectedSize = item.getAttribute('data-size');
      });
    });

    // Handle confirm button
    if (confirmBtn) {
      confirmBtn.addEventListener('click', () => {
        if (selectedSize && sizeBtn) {
          sizeBtn.textContent = `Lấy ngay`;

          // Add gift product to cart with small delay to ensure all scripts are loaded
          setTimeout(() => {
            addGiftProductToCart(selectedSize);
          }, 100);

          closeModal();
        } else {
          // Better user feedback instead of silent fail
          if (confirmBtn) {
            confirmBtn.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
              confirmBtn.style.animation = '';
            }, 500);
          }
        }
      });
    }

    // Close modal with ESC key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modalElement.classList.contains('active')) {
        closeModal();
      }
    });
  };

  // Add to cart function for product detail page (adapted from cart_updater.js)
  const addProductDetailToCart = () => {
    document.addEventListener("click", function (e) {
      const btn = e.target.closest(".btn_add_to_cart");
      if (!btn) return;

      // Check if this is the main product detail page
      const isMainProductDetail = btn.closest('.product-detail__half-layout.product');
      if (!isMainProductDetail) return; // Let other handlers handle non-main products

      const productEl = isMainProductDetail;

      // 1. Product information
      const productId = btn.dataset.productId || "";
      if (!productId) {
        alert("Sản phẩm không hợp lệ");
        return;
      }

      // Get product name from product detail title
      const name = productEl.querySelector(".product-detail__title")?.textContent.trim() || "";
      // Get product image from viewer
      const image = productEl.querySelector(".product__img")?.getAttribute("src") || "";
      // Get product price from product detail price
      const price = productEl.querySelector(".product-detail__price span:first-child")?.textContent.trim() || "";

      // 2. Get selected size only
      const activeSizeEl = productEl.querySelector(".product-size__item.active");
      if (!activeSizeEl) {
        alert("Vui lòng chọn kích thước.");
        return;
      }

      const selectedSize = {
        text: activeSizeEl.textContent.trim(),
        isActive: true,
        dataColor: activeSizeEl.getAttribute("data-color") || null,
      };

      // 3. Get selected color only
      const activeColorEl = productEl.querySelector(".product-color.active");
      if (!activeColorEl) {
        alert("Vui lòng chọn màu sắc.");
        return;
      }

      const selectedColor = {
        backgroundColor: activeColorEl.querySelector(".product-color__item")?.style.backgroundColor || "#000000",
        dataImg: activeColorEl.getAttribute("data-img") || image,
        isActive: true,
      };

      // Create arrays with only selected items
      const sizes = [selectedSize];
      const colors = [selectedColor];

      // 4. Quantity
      const quantityInput = productEl.querySelector(".quantity-input");
      const quantity = quantityInput ? parseInt(quantityInput.value || "1", 10) : 1;

      const productData = {
        product_id: productId,
        name,
        image,
        sizes,
        colors,
        quantity,
        price
      };

      // Use existing cart functionality
      if (typeof cartUpdater !== 'undefined' && cartUpdater.addToCartSmart) {
        cartUpdater.addToCartSmart(productData);

        // Open cart sidebar
        const cartSidebarWrapper = document.querySelector('#cartSidebar');
        if (cartSidebarWrapper) {
          cartSidebarWrapper.classList.add('is-open');
        }
      } else {
        console.error('cartUpdater not available');
        alert('Chức năng giỏ hàng chưa sẵn sàng. Vui lòng tải lại trang.');
      }
    });
  };

  // Initialize gift product size and color selection
  const initGiftProductSelection = () => {
    const giftProduct = document.querySelector('.gift-product');
    if (!giftProduct) return;

    // Handle size selection
    const sizeItems = giftProduct.querySelectorAll('.product-size__item');
    sizeItems.forEach(item => {
      item.addEventListener('click', function (e) {
        // Prevent event bubbling to avoid interference with main product size handler
        e.stopPropagation();
        e.preventDefault();

        // Remove active class from all gift product size items
        sizeItems.forEach(sizeItem => {
          sizeItem.classList.remove('active');
        });

        // Add active class to clicked item
        this.classList.add('active');

        // Update gift product image if needed (in case size affects available colors)
        updateGiftProductImage();
      });
    });

    // Handle color selection
    const colorItems = giftProduct.querySelectorAll('.product-color');
    colorItems.forEach(item => {
      item.addEventListener('click', function (e) {
        // Prevent event bubbling to avoid interference with main product color handler
        e.stopPropagation();
        e.preventDefault();

        // Remove active class from all gift product color items
        colorItems.forEach(colorItem => {
          colorItem.classList.remove('active');
        });

        // Add active class to clicked item
        this.classList.add('active');

        // Update gift product image specifically
        updateGiftProductImage();
      });
    });

    // Handle add to cart button
    const addBtn = giftProduct.querySelector('.gift-product__add-btn');
    if (addBtn) {
      addBtn.addEventListener('click', function () {
        addGiftToCart();
      });
    }
  };

  // Update gift product image based on selected color
  const updateGiftProductImage = () => {
    const giftProduct = document.querySelector('.gift-product');
    if (!giftProduct) {
      console.log('Gift product container not found');
      return;
    }

    const activeColor = giftProduct.querySelector('.product-color.active');
    if (activeColor) {
      const newImg = activeColor.getAttribute('data-img');
      const giftImg = giftProduct.querySelector('.gift-product__image img');

      console.log('Gift color change:', {
        newImg: newImg,
        giftImgFound: !!giftImg,
        currentSrc: giftImg ? giftImg.getAttribute('src') : 'N/A'
      });

      if (giftImg && newImg) {
        giftImg.setAttribute('src', newImg);
        console.log('Gift image updated to:', newImg);
      } else {
        console.log('Gift image update failed - missing image element or src');
      }
    } else {
      console.log('No active color found in gift product');
    }
  };

  // Add gift product to cart
  const addGiftToCart = () => {
    const giftProduct = document.querySelector('.gift-product');
    if (!giftProduct) return;

    // Get selected size
    const selectedSize = giftProduct.querySelector('.product-size__item.active');
    if (!selectedSize) {
      alert('Vui lòng chọn kích thước cho quà tặng.');
      return;
    }

    // Get selected color
    const selectedColor = giftProduct.querySelector('.product-color.active');
    if (!selectedColor) {
      alert('Vui lòng chọn màu sắc cho quà tặng.');
      return;
    }

    // Extract gift product data
    const giftContent = giftProduct.querySelector('.gift-product__content');
    const giftData = extractGiftProductData(giftContent);

    if (!giftData) {
      alert('Không thể lấy thông tin quà tặng. Vui lòng thử lại.');
      return;
    }

    // Update gift data with selected options
    giftData.selectedSize = selectedSize.getAttribute('data-size');
    giftData.selectedColor = selectedColor.getAttribute('data-color');
    giftData.image = selectedColor.getAttribute('data-img');

    // Add to cart using existing cart functionality
    if (typeof cartUpdater !== 'undefined' && cartUpdater.addToCartSmart) {
      try {
        cartUpdater.addToCartSmart(giftData);

        // Show success message
        if (cartUpdater.showGiftAddedMessage) {
          cartUpdater.showGiftAddedMessage('Đã thêm quà tặng vào giỏ hàng!');
        }

        // Open cart sidebar
        const cartSidebarWrapper = document.querySelector('#cartSidebar');
        if (cartSidebarWrapper) {
          cartSidebarWrapper.classList.add('is-open');
        }
      } catch (error) {
        console.error('Error adding gift to cart:', error);
        alert('Có lỗi xảy ra khi thêm quà tặng vào giỏ hàng. Vui lòng thử lại.');
      }
    } else {
      console.error('cartUpdater not available');
      alert('Chức năng giỏ hàng chưa sẵn sàng. Vui lòng tải lại trang.');
    }
  };

  // Initialize combo button event listener
  const initComboButton = () => {
    const comboBtn = document.querySelector('.btn_buy_combo');
    if (comboBtn) {
      comboBtn.addEventListener('click', (e) => {
        e.preventDefault();
        addComboToCart();
      });
    }
  };

  return {
    init: function () {
      initOffcanvasSupport();
      initTabSwitcher();
      changePruductColor();
      initMainProductSizeSelection();
      initAddonProductSizeSelection();
      addProductDetailToCart();
      initCommentFileUploadPreview();
      initStickyHeightSync();
      initRatingClick();
      initGiftSizeModal();
      initGiftProductSelection();
      initComboButton();
    },
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  product_detail.init();
});