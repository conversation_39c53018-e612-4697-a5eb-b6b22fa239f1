/* Privacy Policy Styles */

/* Background Section */
.privacy-bg {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 25em;
    display: flex;
    align-items: center;
    justify-content: center;
}

.privacy-bg--overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.privacy-bg .container {
    position: relative;
    z-index: 2;
    text-align: center;
}

.privacy-bg .content {
    color: var(--white);
}

.privacy-bg .title {
    font-size: 3.2em;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Breadcrumb */
.privacy-breadcrumb {
    background: var(--white);
    padding: 1.5em 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.652vw;
}

.privacy-breadcrumb .list {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5em;
}

.privacy-breadcrumb .item {
    display: flex;
    align-items: center;
    font-size: 1.4em;
    color: var(--text-muted);
}

.privacy-breadcrumb .item:not(:last-child)::after {
    content: '';
    width: 1.2em;
    height: 1.2em;
    margin-left: 0.5em;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='m9 18 6-6-6-6'/%3E%3C/svg%3E") no-repeat center;
    background-size: contain;
}

.privacy-breadcrumb .link {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

.privacy-breadcrumb .link:hover {
    color: var(--primary-hover);
}

/* Privacy Policy Content */
.privacy-policy {
    padding: 4em 0;
    background: var(--white);
    font-size: 0.652vw;
}

.privacy-policy__content {
    margin: 0 auto;
}

.privacy-policy__title {
    font-size: 3.2em;
    font-weight: 700;
    color: var(--text-dark);
    text-align: center;
    margin: 0 0 1.5em 0;
    line-height: 1.2;
}

/* Privacy Section */
.privacy-section {
    margin-bottom: 4em;
    display: flex;
    align-items: flex-start;
    gap: 2em;
}

/* Header with number and title on same line */
.privacy-section .privacy__header {
    display: flex;
    align-items: flex-start;
    gap: 0.5em;
    margin-bottom: 2em;
    flex: 4;
}

.privacy__number {
    flex-shrink: 0;
    background: var(--primary-color);
    color: #1B46AF;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4.8em;
    font-weight: 800;
    flex: 2;
    line-height: 1;
}

.privacy__title {
    font-size: 2.4em;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
    line-height: 1.3;
    flex: 8;
}



.privacy__text {
    flex: 8;
    font-size: 1.6em;
    line-height: 1.6;
    color: var(--text-color);
}

.privacy__text p {
    margin: 0 0 1.5em 0;
}

.privacy__text p:last-child {
    margin-bottom: 0;
}

/* Privacy Lists */
.privacy-list {
    list-style: none;
    padding: 0;
    margin: 1.5em 0;
}

.privacy-list li {
    position: relative;
    padding-left: 2em;
    margin-bottom: 1em;
    line-height: 1.6;
}

.privacy-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2em;
}

.privacy-contact {
    list-style: none;
    padding: 0;
}

.privacy-contact li {
    margin-bottom: 0.8em;
    line-height: 1.5;
}

.privacy-contact a {
    color: var(--primary-color);
    text-decoration: none;
}

.privacy-contact a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .privacy-bg {
        min-height: 20em;
    }

    .privacy-bg .title {
        font-size: 2.4em;
    }

    .privacy-breadcrumb .item {
        font-size: 1.2em;
    }

    .privacy-policy {
        padding: 2em 0;
        font-size: 1.25vw;
    }

    .privacy-policy__title {
        font-size: 2.4em;
        margin-bottom: 2em;
    }

    .privacy-section {
        margin-bottom: 3em;
        display: flex;
        flex-direction: column;
        gap: 1.5em;
    }

    .privacy-section .privacy__header {
        display: flex;
        align-items: center;
        gap: 1.5em;
        margin-bottom: 0;
    }

    .privacy__number {
        font-size: 3.2em;
        font-weight: 800;
        line-height: 1;
        flex-shrink: 0;
        flex: none;
    }

    .privacy__title {
        font-size: 1.8em;
        margin: 0;
    }

    .privacy__text {
        font-size: 1.3em;
    }

    .privacy-contact li {
        font-size: 1.4em;
    }
}

@media (max-width: 575.98px) {
    .privacy-policy {
        padding: 2em 0;
        font-size: 2vw;
    }
    .privacy-breadcrumb {
        font-size: 2vw;
    }
    .privacy-bg .title {
        font-size: 2em;
    }

    .privacy-policy__title {
        font-size: 2em;
    }

    .privacy-section {
        gap: 1.2em;
        margin-bottom: 2.5em;
    }

    .privacy-section .privacy__header {
        gap: 1.2em;
    }

    .privacy__number {
        font-size: 2.8em;
    }

    .privacy__title {
        font-size: 1.6em;
    }

    .privacy__text {
        font-size: 1.2em;
    }
}