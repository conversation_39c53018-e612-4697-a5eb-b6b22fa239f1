package contact

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/contact/components"
	"goweb/views/layouts"
)

templ Contact() {
	@layouts.Master(nil, &[]templ.Component{headContact()}, scriptContact()) {
		@components.ContactBackground()
		@components.ContactContent()
	}
}

templ headContact() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/contact.css") }/>
}

templ scriptContact() {
	<script type="text/javascript" src={templates.AssetURL("/static/js/maybi-ui.js")}></script>
	<script type="text/javascript" src={templates.AssetURL("/static/js/contact.js")}></script>
}
