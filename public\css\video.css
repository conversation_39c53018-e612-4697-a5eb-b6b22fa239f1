.video-section {
  position: relative;
  font-size: 0.652vw;
  padding: 3em 0;
  width: 100%;
  overflow: hidden;
}
.video-container {
  width: 100%;
  height: calc(100vh - 11.7em);
  overflow: hidden;
}
.video__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 100%;
}

.video__play_btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 1;
}

.video__play {
  position: relative;
  font-size: 1.3em;
}

.video__emoji {
  width: 4em;
  height: 4em;
}

.bg-parallax {
  background-attachment: fixed;
  display: flex;
  align-items: center;
  position: relative;
  background-size: cover;
  background-position: top;
}

.youtube-video__wrapper {
  position: fixed;     
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.youtube-video__wrapper iframe {
  width: 100%;
  height: 85em;
}


#player-container {
  position: absolute;
  opacity: 0;
  transition: all 0.5s ease;
}
  
.scale30 {
  transform: scale(1.3);
}

.video__content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 14em;
  height: 14em;
  background: transparent;
  border-radius: 100%;
  animation: identifier 20s linear infinite;
  text-align: center;
}

.border-white {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;
}

.video__content.border-white:after {
  border: 2.5em solid #fff;
}

.video__content:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  border-radius: 50%;
}


@keyframes identifier {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}


.video__content.border-white .text__char {
  color: var(--black);
}

.text__char {
  display: block;
  position: absolute;
  top: 0;
  left: calc(50% - 8.75em / 2);
  height: calc(8.75em / 2);
  width: 8.75em;
  text-transform: uppercase;
  font-weight: 500;
  transform-origin: bottom center;
  transform: rotate(var(--char-rotate));
  color: var(--black);
  font-size: 1.6em;
}

.modal__video {
  width: 90em;
  height: 50em;
  overflow: hidden;
}

.modal__video .modal__body {
  position: relative;
}

.video-embed {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%;
  height: 0;
}

.video-embed iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-section .features__wrapper {
  margin-top: -3.5em;
  background: var(--light-dark);
  border: 0;
}

.video-section .features__wrapper {
  margin-top: -3.5em;
  background: var(--light-dark);
  border: 0;
}

.features__wrapper {
  position: relative;
  z-index: 2;
  border-top: 0.1em solid #000;
  border-bottom: 0.1em solid #000;
  transform: rotate(-2deg);
}

.features .feature__item {
  padding: 1em 2em;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  font-size: 0.652vw;
}
.features .feature__item svg {
  width: 5.5em;
  height: 5.5em;
}

.features__wrapper .features{
  margin-bottom: 0;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  animation: ticker 0s linear infinite 0s normal;
  animation-duration: 0s;
  animation-duration: 32.9528s;
  position: relative;
  z-index: 1;
  padding: 0.5em 2em 0.5em;
}

.features__wrapper:hover .features {
  animation-play-state: paused;
}

.feature__item > h2 {
  color: var(--title);
  font-size: 3em;
  font-weight: 400;
  display: flex;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  justify-content: center;
  line-height: 1;
  width: max-content;
}

@keyframes ticker {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0); }
  100% {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  } 
}

@media (max-width: 768px) {
  .video-container {
    width: 100%;
    height: auto;
    overflow: hidden;
    aspect-ratio: 16 / 9;
  }
  .video__play {
    font-size: 2.5em;
  }
}