.product-detail__wrapper {
  position: relative;
  font-size: var(--10px);
}

.no-animation {
  opacity: 1 !important;
  transform: none !important;
  animation: none !important;
  will-change: auto !important;
}

/* ------- Breadcrumb ------- */
.breadcrumb-box {
  display: flex;
  justify-content: space-between;
  padding: 1em 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  justify-content: center;
}

.breadcrumb-item:not(:first-child)::before {
  content: '';
  display: inline-block;
  width: 1.25em;
  height: 1.25em;
  margin: 0 0.625em;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
  vertical-align: middle;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item > a,
.breadcrumb-item > span {
  font-size: 1.2em;
  font-weight: 500;
}

.product-detail__half-layout {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
}

.product-detail__viewer {
  max-width: 100%;
  overflow: hidden;
  border-radius: 2em;
  aspect-ratio: 3 / 4;
  position: sticky;
  top: 6.5em;
  height: 90svh;
}

.product-detail__viewer > .product-detail__thumbnails {
  position: absolute;
  top: 2em;
  left: 2em;
  bottom: 1em;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 1em;
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.product-detail__viewer > .swiper.swiper-product-detail-img-main,
.product-detail__box,
.product__thumb-img {
  height: 100%;
  border-radius: 2em;
}

.product-detail__thumbnails > img {
  border: 0.1em solid var(--white);
  object-fit: cover;
  width: 5em;
  aspect-ratio: 3 / 4;
  cursor: pointer;
  border-radius: 0.36em;
  transition: 0.3s;
}

.product-detail__thumbnails img.active {
  border-color: var(--primary);
}

.product-detail__box > .product__thumb-img > img {
  object-fit: cover;
  object-position: center;
  height: 100%;
  cursor: pointer;
  border-radius: 2em;
  width: 100%;
}

.product-detail__content-left {
  width: 50%;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.product-detail__content-right {
  width: 50%;
  height: 100%;
  padding-left: 2.5em;
}

.product-detail__content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.product-detail__content > .rating-star {
  width: 6.55em;
}

.product-detail__content-top,
.product-detail__content-bot {
  width: 100%;
}

.product-detail__content-box {
  display: flex;
  gap: 1em;
}

.product-detail__label {
  background: var(--black);
  color: var(--white);
  padding: 0.4em 0.8em;
  margin-bottom: 0.65em;
  font-size: 0.95em;
  border-radius: 1em;
  text-transform: uppercase;
  text-align: center;
  font-weight: 600;
  line-height: normal;
}

.product-detail__label.product-detail__sale-percent {
  background-color: var(--primary);
}

.product-detail__label.product-detail__sale-hot {
  background-color: var(--light-dark);
}

.product-detail__title {
  margin: 0.2em 0 0.2em;
  font-size: 3em;
  font-weight: 500;
}

.product-detail__rating {
  margin-bottom: 0.8em;
  display: flex;
  align-items: center;
  gap: 1.5em;
}

.product-detail__rating small {
  font-size: 1.15em;
}

.product-detail__desc-short {
  font-size: 1.3em;
  margin-bottom: 0.8em;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
}

.product-detail__meta {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 2em;
  flex-wrap: wrap;
}

.product-detail__meta-right {
  width: 100%;
  height: 100%;
}

.product-detail__label-text {
  display: block;
  font-size: 1.3em;
  font-weight: 700;
  color: var(--black);
  background: transparent;
  margin-bottom: 0.5em;
}

.product-detail__quantity-control {
  display: flex;
  align-items: center;
}

.product-detail__quantity-inline {
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.product-detail__btn {
  border: 0.1em solid var(--black);
  background-color: var(--black);
  color: var(--white);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-detail__btn > .product-icon__down,
.product-detail__btn > .product-icon__up {
  font-size: var(--10px);
  width: 3.5em;
  height: 3.5em;
  fill: var(--white);
}

.product-detail__btn > .product-icon__down > use,
.product-detail__btn > .product-icon__up > use {
  transform: scale(0.7);
  transform-origin: center;
}

.product-detail__quantity-input {
  padding: 0.5em;
  font-size: 1.5em;
  text-align: center;
  border: 0.1em solid var(--black);
  width: 2.7em;
  height: 2.7em;
  border-radius: 50%;
}

.product-detail__price {
  font-size: var(--10px);
  line-height: 1;
}

.product-detail__price > span {
  font-size: 2.5em;
  font-weight: 700;
  color: var(--primary);
}

.product-detail__price > span + span {
  font-size: 2em;
  color: rgba(var(--rgb-black), 0.7);
  font-weight: 500;
  margin-left: 0.5em;
  text-decoration: line-through;
}

.product-detail__options {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.product-detail__options.product__info {
  flex-direction: row;
  padding: 0;
  box-shadow: none;
  background-color: transparent;
  gap: 0;
}

.product-detail__options-left,
.product-detail__options-right,
.product-detail__options-center {
  position: relative;
  width: 50%;
}

.product-detail__options-right {
  width: 100%;
}

.product-colors {
  flex-wrap: wrap;
}

.product-detail__options-left > .product-size {
  flex-direction: row;
  width: 50%;
  justify-content: flex-start;
  left: 0;
  bottom: 0;
  opacity: 1;
  background-color: transparent;
  position: unset;
  padding: 0 0 1em;
}

.product-detail__options-right .product-colors {
  gap: 0.5em;
}

.product-detail__support {
  display: flex;
  gap: 2em;
}

.product-detail__actions {
  padding: 0 0 2em;
  display: flex;
  gap: 1em;
  align-items: center;
  border-bottom: 0.1em solid rgba(var(--rgb-black), 0.2);
}

.btn {
  padding: 1.1em 1.75em;
  cursor: pointer;
  border-radius: 0.7em;
  border: 0.1em solid var(--black);
}

.btn--primary > span,
.btn--outline > span {
  font-size: 1.4em;
}

.btn--primary > span,
.btn--outline > span {
  text-transform: uppercase;
}

.btn--primary {
  position: relative;
  overflow: hidden;
  background: var(--black);
  display: flex;
  color: var(--white);
  z-index: 1;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn--primary:hover {
  color: var(--white);
}

.btn--primary::after,
.btn--outline::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.8;
  visibility: hidden;
  height: 100%;
  width: 100%;
  transform: translateX(-105%);
  border-right: 0.2em solid var(--white);
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.5s ease;
  pointer-events: none;
}

.btn--primary:hover::after,
.btn--outline:hover::after {
  transform: translate(0);
  opacity: 0;
  visibility: visible;
}

.btn--outline {
  position: relative;
  overflow: hidden;
  background: var(--white);
  color: var(--black);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1em;
  white-space: nowrap;
  width: 100%;
}

.btn--outline:hover {
  background-color: var(--black);
  color: var(--white);
}

.product-detail__info > p {
  margin: 0.3em 0;
}

.product-detail__info > p > strong,
.product-detail__social-icon > p > strong {
  font-size: 1.4em;
  margin-right: 0.65em;
}

.product-detail__info > p > .comma-item {
  font-size: 1.31em;
}

.product-detail__social-icon {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 1em;
  margin: 1.5em 0;
}

.product-detail__social-icon > p {
  margin-bottom: 0;
}

.product-detail__social-icon > ul {
  display: flex;
  align-items: center;
  gap: 2em;
}

.product-detail__social-icon > ul > li > a {
  display: flex;
  align-items: center;
}

.product-detail__social-icon .product-icon__social-item {
  width: 2em;
  height: 2em;
}

.product-detail__metadata {
  color: rgba(var(--rgb-black), 0.5);
  font-size: 1.2em;
  margin: 1em 0 1.5em;
  display: flex;
  flex-direction: column;
  gap: 0.7em;
}

.product-detail__trademark {
  display: flex;
  align-items: center;
  gap: 2em;
}

.product-detail__trademark > p {
  margin-bottom: 0;
}

.product-detail__trademark .bookmark-btn > .form-check-label {
  height: 2.5em;
  width: 2.5em;
  border-radius: 2em;
}

.product-detail__trademark .product-icon__heart {
  width: 1.8em;
  height: 1.8em;
}

.product-detail__order-benefits,
.order-benefits__item {
  display: flex;
  align-items: flex-start;
  gap: 1.5em;
  margin-top: 0.5em;
}

.order-benefits__item:first-child {
  gap: 0.5em;
}

.order-benefits__item-icon > svg {
  width: 4.5em;
  height: 4.5em;
}

.order-benefits__item:last-child > .order-benefits__item-icon > svg {
  opacity: 0.5;
  stroke: var(--black);
  stroke-width: 10;
  fill: none;
}

.order-benefits__item-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.order-benefits__item-info > span {
  font-size: 1.2em;
  margin-bottom: 0.5em;
}

.order-benefits__item-info > h6 > strong {
  font-size: 2.05em;
}

/* ----------- Addon Deals Product ----------- */
.addon-deals__content-wrapper {
  display: flex;
  width: 100%;
  border: 0.1em solid var(--black);
  padding: 0.5em 1.5em 0.5em 0.5em;
  border-radius: 2em;
}

.addon-deals__thumb {
  width: 16em;
  flex: 3;
  aspect-ratio: 3 / 4;
}

.addon-deals__thumb > img {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
  border-radius: 1.2em;
}

.addon-deals__info {
  width: 100%;
  padding: 0.5em 1em;
  gap: 0.5em;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex: 7;
}

.addon-deals__info-top {
  display: flex;
  justify-content: space-between;
  gap: 0.5em;
  flex-direction: column;
}

.addon-deals__top-title > a {
  font-size: 1.6em;
  font-weight: 700;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  /* margin-bottom: .7em; */
}

.addon-deals__top-tags > p > a {
  font-size: 1em;
  font-weight: 500;
  color: rgba(var(--rgb-black), 0.5);
}

.addon-deals__info-bot {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-direction: column;
  height: 100%;
}

.addon-deals__info-bot > .product__info {
  padding: 0;
  gap: 0;
  background-color: transparent;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: none;
  gap: 0.5em;
}

.addon-deals__price {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2em;
  margin-top: 0.5em;
}

.addon-deals__price > label,
.addon-deals__size > label,
.addon-deals__color > label {
  font-size: 1.2em;
  font-weight: 600;
}

.addon-deals__price > span {
  font-size: 1.3em;
  font-weight: 500;
  color: var(--primary);
}

.addon-deals__price .product-detail__label-text {
  margin-bottom: 0;
}

.addon-deals__price > .product-detail__price > span {
  font-size: 1.6em;
}

.addon-deals__price > .product-detail__price > span + span {
  font-size: 1.1em;
}

.addon-deals__size {
  position: relative;
  display: flex;
  align-items: center;
  gap: 1em;
}

.addon-deals__size > ul {
  position: unset;
  opacity: 1;
  flex-direction: row;
  width: 100%;
  background-color: transparent;
  justify-content: flex-start;
}

.addon-deals__size > ul > li {
  width: 2.5em;
  height: 2.5em;
}

.addon-deals__size > .product-detail__label-text {
  margin-bottom: 0;
  white-space: nowrap;
}

.addon-deals__bot-right {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5em;
  width: 100%;
}

.addon-deals__bot-right > .btn--primary,
.addon-deals__bot-right > .btn--outline {
  padding: 1em;
  margin-right: 0;
}

.addon-deals__bot-right > .btn--primary > span,
.addon-deals__bot-right > .btn--outline > span {
  font-size: 1em;
  font-weight: 500;
  white-space: nowrap;
}

.bookmark-btn .form-check-input {
  display: none;
}

.bookmark-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.bookmark-btn .form-check-label {
  height: 3.5em;
  width: 3.5em;
  cursor: pointer;
  text-align: center;
  display: block;
  background: var(--white);
  border-radius: 3em;
  color: var(--title);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 0.4em 0.6em rgba(0, 0, 0, 0.1);
}

.bookmark-btn .form-check-label:hover {
  background-color: var(--black);
}

.bookmark-btn .form-check-label:hover > svg {
  fill: var(--white);
}

.bookmark-btn .form-check-input:checked + .form-check-label {
  fill: var(--white);
  background-color: var(--primary);
  border: 0.1em solid var(--primary);
}

.bookmark-btn .form-check-label::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.8;
  visibility: hidden;
  height: 100%;
  width: 100%;
  transform: translateX(-105%);
  border-right: 0.2em solid var(--white);
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.5s ease;
  pointer-events: none;
}

.bookmark-btn .form-check-label:hover::after {
  transform: translate(0);
  opacity: 0;
  visibility: visible;
}

.addon-deals__title {
  font-size: 1.5em;
  margin: 0.5em 0;
  color: var(--primary);
}

.addon-deals__color {
  display: flex;
  align-items: center;
  gap: 1em;
  margin-top: 0.5em;
}

.addon-deals__color > .product-colors {
  flex-wrap: wrap;
}

/* .addon-deals__color >.product-colors>li{
  width: 2.5em;
  height: 2.5em;
}
.addon-deals__color .product-color__item{
    width: 1.5em;
    height: 1.5em;
} */
.product-detail__return-policy,
.product-detail__size-guide {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1em;
  cursor: pointer;
  margin: 2em 0;
}

.product-detail__size-guide {
  gap: 0.5em;
}

.product-detail__return-policy > svg,
.product-detail__size-guide > svg {
  width: 3em;
  height: 3em;
}

.product-detail__return-policy > span,
.product-detail__size-guide > span {
  font-size: 1.3em;
  font-weight: 500;
}

/* ------------ Offcanvas ------------ */
.offcanvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.offcanvas__overlay,
.offcanvas__panel {
  transition: all 0.5s ease;
}

.offcanvas__overlay {
  position: absolute;
  inset: 0;
  background: var(--black);
  opacity: 0;
}

.offcanvas__panel {
  position: absolute;
  right: -100%;
  width: 50%;
  max-width: 100%;
  height: 100%;
  background: var(--white);
  padding: 0 0 1.5em;
  box-shadow: -0.2em 0 1em rgba(var(--rgb-black), 0.1);
  overflow-y: auto;
  font-size: var(--10px);
}

.offcanvas__close {
  position: absolute;
  top: 0.4em;
  right: 1em;
  font-size: 2em;
  background: none;
  border: none;
  cursor: pointer;
}

.offcanvas.active {
  pointer-events: auto;
}

.offcanvas.active .offcanvas__overlay {
  background: rgba(var(--rgb-black), 0.5);
  opacity: 1;
}

.offcanvas.active .offcanvas__panel {
  right: 0;
}

.offcanvas__content > h3 {
  font-size: 2em;
  padding: 0.5em 0 0.5em 1em;
  border-bottom: 0.05em solid rgba(var(--rgb-black), 0.2);
}

/* ------- Offcanvas Return Policy ------- */
.policy-content {
  line-height: 1.6;
  color: var(--black);
  padding: 1em 3em 0 3em;
  text-align: justify;
}

.policy-content > p {
  margin-bottom: 1em;
  font-size: 1.6em;
}

.policy-content > ul {
  padding-left: 1.2em;
  margin-bottom: 1em;
}

.policy-content > ul > li {
  margin-bottom: 0.5em;
  font-size: 1.6em;
}

.policy-content > h4 {
  font-size: 1.9em;
  margin: 1em 0 0.5em;
  color: var(--primary);
  font-weight: 600;
}

.policy-content > h5 {
  font-size: 1.8em;
  margin: 1em 0 0.3em;
  font-weight: 600;
}

.policy-highlight > strong {
  font-size: 1.6em;
  color: var(--primary);
  margin: 1em 0;
}

/* ------- Tab panes description and review ------- */
.tab-panes {
  font-size: var(--10px);
  padding-top: 4em;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* ------- Tab description ------- */
.desc-rate__wrapper {
  font-size: var(--10px);
  padding: 5% 0;
}

.nav-product-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 1em;
  margin-bottom: 1.5em;
  border-bottom: 0.15em solid rgba(var(--rgb-black), 0.2);
  padding-left: 15%;
}

.desc__box {
  display: flex;
}

.nav-product-detail > .nav-item {
  list-style: none;
}

.nav-product-detail .nav-link {
  width: 100%;
  padding: 0.5em 1em;
  text-align: start;
  font-size: 1.4em;
  font-weight: 500;
  border-radius: 0;
  background-color: transparent;
  border: 0;
  border-bottom: 0.1em solid transparent;
  color: var(--black);
  box-shadow: unset;
  cursor: pointer;
  position: relative;
  margin-top: 0.1em;
}

.nav-product-detail .nav-link:hover {
  color: var(--color-body);
}

.nav-product-detail .nav-link.active {
  border-bottom: 0.1em solid var(--black);
  color: var(--black);
}

.desc-center__top > h2 {
  font-size: 3.5em;
  font-weight: 600;
  margin-bottom: 0.5em;
}

.desc__content,
.desc__content-video {
  display: flex;
  flex-direction: column;
  gap: 1em;
  padding-left: 1em;
}

.desc__item {
  margin-bottom: 1em;
  font-size: var(--10px);
}

.desc__item > p,
.desc__item > strong {
  font-size: 1.4em;
}

.desc__content-video > iframe {
  width: 100%;
}

.desc__box-center {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 2em;
}

.desc-right__center {
  display: flex;
  justify-content: center;
  align-items: stretch;
  flex-wrap: wrap;
  gap: 2.5em;
}

.desc-right__center-item {
  width: 48%;
  padding: 2em;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 2em;
  border: 0.1em solid var(--black);
}

.desc-right__center-item > h4 {
  font-size: 2em;
  font-weight: 700;
  margin-bottom: 0.5em;
}

.desc-right__center-item > p {
  font-size: 1.2em;
  text-align: justify;
}

.desc-right__bot-thumb {
  height: 60em;
  width: 100%;
}

.desc-right__bot-thumb > img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 2em;
}

.desc__content-video {
  position: relative;
  width: 100%;
  padding-top: 55%;
}

.desc__content-video iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

/* ------- Tab Review ------- */
.review__comment-title,
.comment-reply-title {
  font-size: 1.55em;
  font-weight: 600;
  margin-bottom: 0.5em;
}

.review__comment-area > .comment-list {
  margin-bottom: 5.5em;
}

.review__comment-area p {
  font-size: 1.3em;
}

.review__comment-area > p {
  margin-bottom: 0;
}

.review__comment-area > p,
.comment-respond > p {
  color: rgba(var(--rgb-black), 0.5);
}

.comment-respond__text,
.review__comment-area_text {
  margin-bottom: 2em;
}

.comment-body {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 1em;
  border-bottom: 0.1em solid rgba(var(--rgb-black), 0.1);
  padding: 2em 0;
}

.comment__avatar-thumb {
  width: 7em;
  height: 7em;
}

.comment__avatar-thumb > img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
}

.comment-content > span {
  font-size: 1.5em;
  font-weight: 600;
  margin-bottom: 1em;
}

.comment-content > p {
  font-size: 1.2em;
  color: rgba(var(--rgb-black), 0.6);
  text-align: justify;
}

.comment-body > .reply > a {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 1.05em;
  white-space: nowrap;
}

.comment-body > .reply {
  background-color: var(--black);
  color: var(--white);
  padding: 0.5em 1em;
  border-radius: 0.5em;
}

.comment-body > .reply > .comment-reply-link:hover {
  color: var(--white);
}

.comment-list .comment > ul {
  padding-left: 4.3em;
}

.comment-respond .comment-form {
  display: flex;
  flex-direction: column;
  font-size: var(--10px);
}

.comment-form__author-phone,
.comment-form__text,
.comment-form__file,
.comment-form__submit {
  margin-bottom: 1.5em;
}

.comment-form__author-phone {
  display: flex;
  align-items: center;
}

.comment-form__author,
.comment-form__phone {
  width: 50%;
}

.comment-form__text {
  width: 100%;
}

.comment-form__author > input,
.comment-form__phone > input,
.comment-form__text > textarea {
  width: 100%;
  height: 3.5em;
  padding: 0.7em 1.35em;
  font-size: 1.3em;
  border: 0.07em solid var(--secondary);
  background: transparent;
  border-radius: 0.7em;
}

.comment-form__text > textarea {
  max-height: 25em;
  min-height: 10em;
  resize: vertical;
}

.comment-form__author {
  padding-right: 0.5em;
}

.comment-form__phone {
  padding-left: 0.5em;
}

/* Intro product */
.product-detail__promotion {
  position: relative;
  border: 0.1em dashed var(--primary);
  padding: 1em;
  border-radius: 0.2rem;
  margin: 2.5em 0 2em;
}

.product-promotion__heading {
  position: absolute;
  top: -1.4em;
  font-size: 1.2em;
  font-weight: 700;
  padding: 0.5em 1em;
  color: var(--primary);
  background: var(--light);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 0.7em;
}

.product-detail__promotion > ul {
  margin-bottom: 0;
  border-radius: 0.2em;
  padding: 0.8em 0.8em 0.8em 1.3em;
  list-style-position: inside;
  list-style-type: disc;
  background: var(--light);
  font-size: 1.2em;
}

.product-detail__promotion > ul > li {
  padding: 0.3em 0;
  margin: 0;
}

.product-promotion__icon {
  width: 1.4em;
  height: 1.4em;
}

.product-rating__title {
  font-size: 1.4em;
  font-weight: 500;
}

.comment-form .mb-rating {
  margin-bottom: 1em;
  margin: 1em 0 1.5em;
  cursor: pointer;
}

.comment-form .mb-star {
  width: 2em;
  height: 2em;
}

/* ------ 2 Btn Related ------ */
.swiper-button-next.related-product__next,
.swiper-button-prev.related-product__prev {
  width: 4em;
  height: 4em;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  font-size: var(--10px);
}

.swiper-button-next.related-product__next:after,
.swiper-button-prev.related-product__prev:after {
  font-size: 2em;
  color: rgb(var(--rgb-white));
}

/* ------- Size chart offcanvas ------- */
.offcanvas-template {
  font-size: var(--10px);
  margin-bottom: 2em;
}

.offcanvas__content > p {
  font-size: 1.6em;
  margin-top: 1em;
  padding-left: 2em;
}

.size-chart__bg {
  padding: 0 3em;
}

.size-chart__table,
.size-chart__table > table {
  width: 95%;
}

.size-chart__table > table {
  border-collapse: collapse;
  border: 0.1em solid rgba(var(--rgb-black), 0.5);
  width: 100%;
}

.size-chart__table th,
.size-chart__table td {
  border: 1px solid rgba(var(--rgb-black), 0.5);
  text-align: center;
  vertical-align: middle;
  padding: 1em;
  font-size: 1.4em;
}

.size-chart__clothes,
.size-chart__fabric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2em;
  margin-top: 2.5em;
  padding: 1em 0;
  background-color: var(--bg-light);
}

.size-chart__clothes > h3,
.size-chart__fabric > h3 {
  font-size: 2.5em;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--primary);
}

.related-product__swiper {
  margin-bottom: 3em;
}

/* ------- Related product ------- */
.related-product__header .sec-head > .sec-head__btn > a {
  font-size: 1.3em;
}

.product-detail__content-top .product-rating {
  margin-bottom: 1em;
  cursor: pointer;
}

.addon-deals__top-right {
  padding-top: 0.2em;
}

.addon-deals__top-right .product-rating {
  align-items: center;
}

.addon-deals__top-right .product-rating > .product-rating__selled {
  white-space: nowrap;
}

.addon-deals__top-right .bookmark-btn {
  justify-content: flex-end;
  margin-top: 1em;
}

.addon-deals__top-right .bookmark-btn .form-check-label {
  width: 2.3em;
  height: 2.3em;
}

.addon-deals__top-right .bookmark-btn .form-check-label > svg {
  width: 1.7em;
  height: 1.7em;
}

.comment-file__label {
  display: inline-block;
  padding: 0.75em 1.5em;
  background-color: var(--primary, #000);
  color: var(--white);
  border-radius: 0.4em;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  user-select: none;
  margin-right: 10px;
}

.comment-file__label:hover {
  background-color: var(--primary-dark, var(--color-body));
}

.comment-form__preview {
  font-size: var(--10px);
}

.comment-form__preview img,
.comment-form__preview video {
  border-radius: 0.5em;
  vertical-align: middle;
}

.preview-wrapper {
  display: inline-block;
  position: relative;
  margin-right: 1em;
  margin-bottom: 1em;
}

.preview-thumb {
  display: block;
  width: 18em;
  height: 10em;
  object-fit: cover;
  object-position: center;
  border-radius: 0.5em;
  box-shadow: 0 0.5em 1.2em rgba(var(--rgb-black), 0.15);
}

.preview-remove {
  position: absolute;
  top: 0.2em;
  right: 0.2em;
  background: var(--white);
  border: 0.077em solid var(--white);
  border-radius: 50%;
  cursor: pointer;
  width: 1.8em;
  height: 1.8em;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-shadow: 0 0.5em 1.2em rgba(var(--rgb-black), 0.15);
  transition: background 0.2s ease;
}

.preview-remove:hover {
  background: var(--primary);
}

.preview-remove > svg {
  width: 1em;
  height: 1em;
  fill: var(--black);
  pointer-events: none;
}

.preview-remove:hover > svg {
  fill: var(--white);
}

/* ---------- Another deals ---------- */
.card {
  position: relative;
  font-size: 0.85em;
  margin-bottom: 1.5em;
  gap: 0.5em;
  display: flex;
  align-items: center;
  border-radius: 0.4em;
  border: 0.1em dashed var(--primary);
  padding: 0.5em 0 0.5em;
}

.loader {
  color: var(--dark-grey);
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-size: 1.6em;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  height: 1.2em;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.another-deals .icon-sales {
  height: 3em;
  aspect-ratio: 1;
  margin-left: 1em;
}

.icon-sales svg {
  height: inherit;
  width: inherit;
  fill: var(--primary);
}

.words {
  overflow: hidden;
  position: relative;
}

.words::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    var(--bg-color) 10%,
    transparent 30%,
    transparent 70%,
    var(--bg-color) 90%
  );
  z-index: 20;
}

.word {
  display: block;
  padding-left: 0.6em;
  color: var(--primary);
  animation: spin_4991 8s infinite;
  height: 100%;
  line-height: 1.25;
}

@keyframes spin_4991 {
  10% {
    -webkit-transform: translateY(-102%);
    transform: translateY(-102%);
  }

  25% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
  }

  35% {
    -webkit-transform: translateY(-202%);
    transform: translateY(-202%);
  }

  50% {
    -webkit-transform: translateY(-200%);
    transform: translateY(-200%);
  }

  60% {
    -webkit-transform: translateY(-302%);
    transform: translateY(-302%);
  }

  75% {
    -webkit-transform: translateY(-300%);
    transform: translateY(-300%);
  }

  85% {
    -webkit-transform: translateY(-402%);
    transform: translateY(-402%);
  }

  100% {
    -webkit-transform: translateY(-400%);
    transform: translateY(-400%);
  }
}

/* ---------- Responsive ---------- */
@media (max-width: 1024px) {
  .product-detail__viewer {
    width: 100%;
    height: auto;
  }
}

@media (max-width: 768px) {
  .comment-form__file,
  .offcanvas__content {
    font-size: var(--10px);
  }

  .product-detail__half-layout {
    flex-direction: column;
  }

  .product-detail__content-left,
  .product-detail__content-right,
  .desc-right__center-item {
    width: 100%;
  }

  .product-detail__content-right {
    padding: 1em 0 0;
  }

  .product-detail__viewer {
    position: relative;
    max-width: 100%;
    top: 0;
    height: 70svh;
  }

  .product-detail__viewer > .product-detail__thumbnails {
    right: 1em;
    left: unset;
  }

  .product-detail__thumbnails > img {
    width: 8em;
  }

  .product-detail__metadata {
    margin: 1em 0;
  }

  .product-detail__metadata > p {
    display: none;
  }

  .product-detail__options-left > .product-size,
  .product-detail__quantity-inline {
    gap: 1em;
    width: inherit;
  }

  .product-detail__options-right .product-colors {
    gap: 0.5em;
  }

  .addon-deals__color > .product-colors {
    font-size: 0.75em;
  }

  .addon-deals__size > ul {
    font-size: 0.75em;
  }

  /* --- Offcanvas --- */
  .offcanvas__panel {
    position: relative;
    overflow-y: auto;
    height: 100%;
    width: 100%;
  }

  .offcanvas__close {
    position: sticky;
    top: 66%;
    left: 90%;
    background: var(--primary);
    color: var(--white);
    width: 1.5em;
    height: 1.5em;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: baseline;
    z-index: 10;
  }

  .offcanvas__content {
    position: absolute;
    top: 1em;
    left: 0;
    right: 1em;
    bottom: 0;
    height: max-content;
    padding-bottom: 1em;
  }

  /* --- Description --- */
  .desc-center__top > h2,
  .review__comment-title,
  .comment-reply-title {
    font-size: 2.5em;
  }

  .tab-panes {
    display: flex;
    justify-content: center;
    padding-top: 2em;
  }

  .tab-pane.active {
    width: 100%;
  }

  .nav-product-detail {
    padding-left: 0;
  }

  .desc__content {
    margin-bottom: 2em;
  }

  /* .desc__content,
  .desc-right__center,
  .desc-right__bot {
    padding-left: 1em;
  } */

  .desc-right__center {
    display: grid;
    grid-template-columns: auto auto;
  }

  /* --- Review --- */
  .comment-form__author-phone {
    flex-direction: column;
    gap: 2.5em;
  }

  .comment-form__author,
  .comment-form__phone {
    width: 100%;
  }

  .comment-form__author {
    padding-right: 0;
  }

  .comment-form__phone {
    padding-left: 0;
  }

  .comment-body {
    gap: 1.5em;
  }

  .comment-form__author-phone,
  .comment-form__text,
  .comment-form__submit {
    margin-bottom: 2.5em;
  }

  .btn-secondary,
  .comment-file__label {
    font-size: 1.3em;
  }

  .preview-remove {
    width: 1.5em;
    height: 1.5em;
  }

  .preview-remove > svg {
    width: 0.8em;
    height: 0.8em;
  }
  .product-detail__options-right{
    width: 100%;
  }
}

@media (max-width: 576px) {
  .product-detail__viewer {
    height: 85svh;
  }

  .breadcrumb-box {
    font-size: 2vw;
  }

  .product-detail__content-right {
    font-size: 2.5vw;
  }

  .product-detail__content-box {
    font-size: 1.2em;
  }

  .product-detail__content-top .product-rating {
    font-size: 1.2em;
  }

  .product-detail__metadata {
    font-size: 1.2em;
  }

  .product-detail__price {
    font-size: 1em;
  }

  .product-detail__btn {
    font-size: 2.5em;
  }

  .product-detail__options.product__info {
    font-size: 2.5vw;
  }

  .product-detail__quantity-input {
    font-size: 1.2em;
  }

  .product-detail__options-right .product-colors {
    gap: 1em;
    font-size: 2vw;
  }

  .product-detail__actions {
    font-size: 1.1em;
  }

  .addon-deals__thumb {
    flex: 4;
  }

  .addon-deals__info {
    flex: 6;
    font-size: 1.1em;
  }

  .addon-deals__info-bot > .product__info {
    font-size: 1.75vw;
  }

  .nav-product-detail {
    font-size: 1.75em;
  }

  .desc__item {
    font-size: 1em;
  }

  .desc-center__top > h2,
  .review__comment-title,
  .comment-reply-title {
    font-size: 2.2em;
  }

  .tab-panes {
    padding-top: 0.5em;
    font-size: 1.5em;
  }

  .comment-respond {
    font-size: 1.2em;
  }

  .comment-respond .comment-form {
    font-size: 1.1em;
  }

  .comment-form__file,
  .offcanvas__content {
    font-size: 1.1em;
  }
  .product-detail__options-left .product-size__item{
    width: 2.2em;
    aspect-ratio: 1 / 1;
    height: 2.2em;
    font-size: 1.5em;
  }
  .product-detail__options-left > .product-size {
    gap: 1em;
    width: fit-content;
  }
}

/* Gift Product Component */
.gift-product {
  border-radius: 1em;
  padding: 3em 1em 1em;
  margin: 1em 0;
  position: relative;
  overflow: hidden;
  border: 0.1em solid var(--primary);
  font-size: 1.2em;
}

.gift-product__badge {
  display: flex;
  align-items: center;
  gap: 0.5em;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5em 1em;
  font-size: 0.875em;
  font-weight: 600;
  color: white;
  margin-bottom: 1em;
  width: fit-content;
  position: absolute;
  top: 0;
  left: 0;
  border-bottom-right-radius: 1.75em;
}

.gift-product__icon {
  width: 1.5em;
  height: 1.5em;
  color: white;
}

.gift-product__content {
  display: flex;
  gap: 1em;
  align-items: center;
}

.gift-product__image {
  height: 15em;
  aspect-ratio: 3 / 4;
  border-radius: 0.75em;
  overflow: hidden;
  flex-shrink: 0;
}

.gift-product__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.5em;
}

.gift-product__info {
  flex: 1;
  color: var(--black);
  display: flex;
  flex-direction: column;
  gap: 0.75em;
}

.gift-product__details {
  flex: 1;
  font-size: 1.1em;
}

.gift-product__actions {
  display: none;
  /* display: flex; */
  justify-content: flex-start;
}

.gift-product__title {
  font-size: 1.5em;
  font-weight: 600;
  margin: 0 0 0.25em 0;
  line-height: 1.3;
}

.gift-product__desc {
  font-size: 0.85em;
  margin: 0 0 0.5em 0;
  opacity: 0.9;
  line-height: 1.4;
}

.gift-product__price {
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.gift-product__price-current {
  font-size: 1.25em;
  font-weight: 700;
  color: var(--primary);
}

.gift-product__price-original {
  font-size: 0.875em;
  text-decoration: line-through;
  opacity: 0.7;
  color: var(--black);
}
.gift-product__options {
  /* margin-top: 1em; */
  display: flex;
  flex-direction: column;
  /* gap: 1em; */
}

.gift-product__size-selection,
.gift-product__color-selection {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
  font-size: 0.65em;
}

.gift-product__label {
  font-size: 1.4em;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 0.5em;
}
.gift-product__size-selection > .product-size {
  flex-direction: row;
  width: 50%;
  justify-content: flex-start;
  left: 0;
  bottom: 0;
  opacity: 1;
  background-color: transparent;
  position: unset;
  padding: 0 0 1em;
}
.gift-product__add-btn {
  width: fit-content;
  font-size: 0.65em;
}

.gift-modal .product-size {
  width: 100%;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .gift-product__size-selection,
  .gift-product__color-selection {
    gap: 0.4em;
  }

  .gift-product__label {
    font-size: 1.2em;
    font-weight: 600;
  }

}

/* Gift Size Modal */
.gift-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1055;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.gift-modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.gift-modal {
  background: white;
  border-radius: 1em;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  max-width: 50em;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.7);
  transition: transform 0.3s ease;
}

.gift-modal-overlay.active .gift-modal {
  transform: scale(1);
}

.gift-modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1em 1.5em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.gift-modal__title {
  margin: 0;
  font-size: 1.25em;
  font-weight: 600;
  color: var(--black);
}

.gift-modal__close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25em;
  color: rgba(0, 0, 0, 0.5);
  transition: color 0.3s ease;
}

.gift-modal__close:hover {
  color: var(--black);
}

.gift-modal__body {
  padding: 1.5em;
}

.gift-modal__footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1em;
  padding: 1em 1.5em;
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.gift-modal__footer .btn {
  max-width: 15em;
  font-size: 1em;
}

.gift-modal__footer .btn--outline {
  margin-right: 0;
}

.gift-modal__footer .btn--primary {
  margin-right: 0;
}

.gift-size-selector {
  display: flex;
  flex-direction: column;
  gap: 1.5em;
}

.gift-size-product {
  display: flex;
  align-items: center;
  gap: 1em;
  padding: 1em;
  background: #f8f9fa;
  border-radius: 0.5em;
}

.gift-size-product img {
  object-fit: cover;
  border-radius: 0.375em;
  height: 100%;
  height: 15vw;
  aspect-ratio: 3 / 4;
}
.gift-size-info {
  font-size: 1.5em;
}
.gift-size-info h6 {
  margin: 0 0 0.25em 0;
  font-size: 1em;
  font-weight: 600;
  color: var(--black);
}

.gift-size-info p {
  margin: 0;
  font-size: 0.875em;
  color: rgba(var(--rgb-black), 0.7);
}

.gift-size-options {
  display: flex;
  flex-direction: column;
  gap: 0.75em;
}

.gift-size-label {
  font-size: 1em;
  font-weight: 600;
  color: var(--black);
  margin: 0;
}

.gift-size-list {
  flex-direction: row;
  justify-content: flex-start;
  opacity: 1;
  background-color: transparent;
  position: unset;
  padding: 0;
  display: flex;
  gap: 0.6em;
  margin: 0;
  list-style: none;
}

.gift-size-list .product-size__item {
  font-size: 1.3em;
  width: 3em;
  height: 3em;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: 500;
  border: 0.1em solid rgba(var(--rgb-black), 0.2);
  background-color: rgba(var(--rgb-white), 0.3);
  transition: all 0.3s ease;
}

.gift-size-list .product-size__item.product-size--active {
  background-color: var(--primary);
  color: var(--white);
}

.gift-size-list .product-size__item:hover {
  background-color: var(--black);
  color: var(--white);
}

/* Mobile responsive for gift modal */
@media (max-width: 768px) {
  .gift-modal {
    width: 95%;
  }

}

/* Gift Cart Item Styling */
.cart__item.gift-item {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(255, 107, 157, 0.1) 0%,
    rgba(255, 138, 155, 0.1) 100%
  );
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 1em;
  padding: 1em;
  margin-bottom: 1em;
  overflow: hidden;
}

.gift-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
  color: white;
  padding: 0.25em 0.75em;
  border-radius: 0;
  font-size: 0.95em;
  font-weight: 600;
  z-index: 2;
  border-bottom-right-radius: 1em;
}

/* Gift item styling for both sidebar and main cart */
.cart__item.gift-item .cart__item__price--sale {
  color: #ff6b9d !important;
  font-weight: 700 !important;
}

.cart__item.gift-item .cart__item__total {
  color: #ff6b9d !important;
  font-weight: 700 !important;
}

.cart__item.gift-item .product-size__item.product-size--active {
  background-color: #ff6b9d !important;
  border-color: #ff6b9d !important;
  color: white !important;
}

/* Gift Notification */
.gift-notification {
  position: fixed;
  top: 2em;
  right: 2em;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
  color: white;
  padding: 1em 1.5em;
  border-radius: 0.5em;
  font-weight: 600;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 9999;
}

.gift-notification.show {
  transform: translateX(0);
  opacity: 1;
}
/* Gift Item in Sidebar Cart */
#cartSidebar .cart__item.gift-item {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 157, 0.1) 0%,
    rgba(255, 138, 155, 0.1) 100%
  );
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 1em;
  padding: 1em;
  margin-bottom: 1em;
  overflow: hidden;
}

.gift-item {
  position: relative;
}

.gift-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
  color: white;
  padding: 0.25em 0.75em;
  border-radius: 0;
  font-size: 0.95em;
  font-weight: 600;
  border-bottom-right-radius: 1em;
}

#cartSidebar .gift-item {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 157, 0.1) 0%,
    rgba(255, 138, 155, 0.1) 100%
  );
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 1em;
  padding: 1em;
  margin-bottom: 1em;
}

#cartSidebar .gift-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
  color: white;
  padding: 0.25em 0.75em;
  border-radius: 0;
  font-size: 0.95em;
  font-weight: 600;
  border-bottom-right-radius: 1em;
}

#cartSidebar .gift-price-free {
  color: #ff6b9d !important;
  font-weight: 700 !important;
}

/* Main Cart Page Gift Items */
.products-cart .gift-item {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 157, 0.1) 0%,
    rgba(255, 138, 155, 0.1) 100%
  );
  border: 1px solid rgba(255, 107, 157, 0.3);
  border-radius: 1em;
  padding: 1em;
  margin-bottom: 1em;
}

.products-cart .gift-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
  color: white;
  padding: 0.25em 0.75em;
  border-radius: 0;
  font-size: 0.95em;
  font-weight: 600;
  border-bottom-right-radius: 1em;
}

.products-cart .gift-price-free {
  color: #ff6b9d !important;
  font-weight: 700 !important;
}

.products-cart .gift-total {
  color: #ff6b9d !important;
  font-weight: 700 !important;
}

/* Mobile responsive for gift items */
@media (max-width: 768px) {
  .gift-notification {
    top: 1em;
    right: 1em;
    left: 1em;
    transform: translateY(-100%);
  }

  .gift-notification.show {
    transform: translateY(0);
  }

  .gift-badge {
    font-size: 0.6875em;
    padding: 0.2em 0.5em;
  }
}

/* Button shake animation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}