/* About Page Styles */

/* Breadcrumb Section */
.about-breadcrumb {
    background: var(--white);
    padding: 1.25em 0;
    border-bottom: 0.0625em solid #E5E5E5;
    font-size: 0.652vw;
}

.about-breadcrumb .list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}

.about-breadcrumb .item {
  color: var(--rgb-black);
  font-size: 1.2em;
  font-weight: 500;
}

.about-breadcrumb .item + .item::before {
  content: "";
  display: inline-block;
  width: 1.25em;
  height: 1.25em;
  margin: 0 0.625em;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
  vertical-align: middle;
}

.about-breadcrumb .link {
  color: var(--rgb-black);
  text-decoration: none;
  transition: color 0.3s ease;
}

.about-breadcrumb .link:hover {
  color: var(--rgba-primary-8);
  text-decoration: underline;
}

/* About Page Content */
.about-page {
  padding: 4em 0;
  background: var(--white);
  font-size: 0.652vw;
}

.about-page__content {
  margin: 0 auto;
}

/* Hero Section */
.about-hero {
  position: relative;
  text-align: center;
  margin-bottom: 4em;
}

.about-hero__overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  text-align: center;
}

.about-hero__logo {
  margin-bottom: 2em;
}

.logo-img {
  max-width: 30em;
  height: auto;
  filter: invert(1);
}

.about-hero__title {
  font-size: 6em;
  font-weight: 700;
  color: var(--white);
  margin: 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.about-hero__images {
  display: flex;
  justify-content: center;
  gap: 2em;
  margin-bottom: 3em;
}

.hero-image {
  width: 50%;
  aspect-ratio: 1600 / 1265;
  flex: 0 0 auto;
  border-radius: 1em;
  overflow: hidden;
  box-shadow: 0 0.5em 2em rgba(0, 0, 0, 0.1);
}

.hero-img {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
}

/* About Description */
.about-description {
  max-width: 120em;
  margin: 0 auto 4em auto;
  text-align: center;
}

.about-description p {
  font-size: 1.6em;
  line-height: 1.8;
  color: var(--text-dark);
  margin-bottom: 2em;
}

.about-description p:last-child {
  margin-bottom: 0;
}

/* Features Section */
.about-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3em;
  margin-top: 4em;
}

.feature-item {
  text-align: center;
}

.feature-image {
  margin-bottom: 2em;
  border-radius: 1em;
  overflow: hidden;
  box-shadow: 0 0.5em 2em rgba(0, 0, 0, 0.1);
}

.feature-img {
  width: 100%;
  height: 20em;
  object-fit: cover;
  display: block;
}

.feature-title {
  font-size: 1.8em;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .logo-img {
    max-width: 25em;
  }

  .about-hero__title {
    font-size: 5em;
  }
}

@media (max-width: 992px) {
  .about-features {
    grid-template-columns: 1fr;
    gap: 2.5em;
  }

  .about-hero__images {
    flex-direction: column;
    align-items: center;
    gap: 1.5em;
  }

  .hero-image {
    width: 70%;
  }

  .logo-img {
    max-width: 20em;
  }

  .about-hero__title {
    font-size: 4em;
  }

  .about-description {
    max-width: 80em;
  }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .about-breadcrumb {
    font-size: 1.75vw;
  }
}

@media (max-width: 768px) {
  .about-page {
    padding: 2em 0;
    font-size: 1.25vw;
  }

  .hero-image {
    width: 90%;
  }

  .logo-img {
    max-width: 15em;
  }

  .about-hero__title {
    font-size: 3em;
  }

  .about-breadcrumb {
    font-size: 1.25vw;
  }

  .about-description {
    max-width: 90%;
  }

  .about-description p {
    font-size: 1.4em;
  }

  .feature-title {
    font-size: 1.6em;
  }

  .feature-img {
    height: 18em;
  }
}

@media (max-width: 575.98px) {
  .about-page {
    padding: 2em 0;
    font-size: 2vw;
  }

  .hero-image {
    width: 95%;
  }

  .logo-img {
    max-width: 10em;
  }

  .about-hero__title {
    font-size: 2.2em;
  }

  .about-description {
    max-width: 95%;
  }

  .about-description p {
    font-size: 1.2em;
  }

  .feature-title {
    font-size: 1.4em;
  }

  .about-hero__images {
    gap: 1em;
  }

  .feature-img {
    height: 15em;
  }
}

@media (max-width: 480px) {
  .about-page {
    font-size: 2.5vw;
  }
  .about-breadcrumb {
      font-size: 2vw;
  }
  .logo-img {
    max-width: 8em;
  }

  .about-hero__title {
    font-size: 1.8em;
  }

  .about-hero__logo {
    margin-bottom: 1.5em;
  }

  .about-description p {
    font-size: 1.1em;
  }

  .feature-title {
    font-size: 1.2em;
  }

  .feature-img {
    height: 12em;
  }
}
