package handlers

import (
	"goweb/views/product"

	"github.com/gofiber/fiber/v2"
	"github.com/networld-solution/gos/templates"
)

type productDetailHdl struct {
}

func NewProductDetailHdl() *productDetailHdl {
	return &productDetailHdl{} 
}

/**
 * product detail hdl
 */
func (h *productDetailHdl) ProductDetailHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		return templates.Render(c, product.DetailProduct())

	}
}