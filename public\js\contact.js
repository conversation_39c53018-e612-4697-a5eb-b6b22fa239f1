/**
 * Contact Page JavaScript
 */
document.addEventListener('DOMContentLoaded', function () {
    // Contact form handling
    const contactForm = document.getElementById('contactForm');

    if (contactForm) {
        contactForm.addEventListener('submit', function (e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(contactForm);
            const formInputs = contactForm.querySelectorAll('input, textarea');
            const submitButton = contactForm.querySelector('.form-submit');

            // Disable form during submission
            formInputs.forEach(input => input.disabled = true);
            submitButton.disabled = true;
            submitButton.textContent = 'Đang gửi...';

            // Simulate form submission (replace with actual API call)
            setTimeout(() => {
                // Reset form
                contactForm.reset();

                // Re-enable form
                formInputs.forEach(input => input.disabled = false);
                submitButton.disabled = false;
                submitButton.textContent = '<PERSON><PERSON><PERSON> liên hệ';

                // Show success message
                showNotification('Cảm ơn bạn đã liên hệ! <PERSON>úng tôi sẽ phản hồi trong thời gian sớm nhất.', 'success');
            }, 2000);
        });
    }

    // Form validation
    const formInputs = document.querySelectorAll('.form-input, .form-textarea');

    formInputs.forEach(input => {
        input.addEventListener('blur', function () {
            validateField(this);
        });

        input.addEventListener('input', function () {
            if (this.classList.contains('error')) {
                validateField(this);
            }
        });
    });

    // Smooth scroll animation for contact methods
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe contact elements
    const contactElements = document.querySelectorAll('.contact-method, .contact-form-section');
    contactElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });

    // Add stagger animation for contact methods
    const contactMethods = document.querySelectorAll('.contact-method');
    contactMethods.forEach((method, index) => {
        method.style.transitionDelay = `${index * 0.1}s`;
    });

    // Phone number formatting
    const phoneInput = document.querySelector('input[type="tel"]');
    if (phoneInput) {
        phoneInput.addEventListener('input', function (e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.length <= 3) {
                    value = value;
                } else if (value.length <= 6) {
                    value = value.slice(0, 3) + '.' + value.slice(3);
                } else {
                    value = value.slice(0, 3) + '.' + value.slice(3, 6) + '.' + value.slice(6, 10);
                }
            }
            e.target.value = value;
        });
    }
});

// Field validation function
function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldTag = field.tagName.toLowerCase();
    let isValid = true;
    let errorMessage = '';

    // Remove existing error styling
    field.classList.remove('error');
    removeErrorMessage(field);

    // Required field validation - skip for textarea (content field)
    if (field.hasAttribute('required') && !value && fieldTag !== 'textarea') {
        isValid = false;
        errorMessage = 'Trường này là bắt buộc';
    }

    // Email validation
    else if (fieldType === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'Email không hợp lệ';
        }
    }

    // Phone validation
    else if (fieldType === 'tel' && value) {
        const phoneRegex = /^[\d\.\s\-\+\(\)]+$/;
        if (!phoneRegex.test(value) || value.replace(/\D/g, '').length < 10) {
            isValid = false;
            errorMessage = 'Số điện thoại không hợp lệ';
        }
    }

    if (!isValid) {
        field.classList.add('error');
        showErrorMessage(field, errorMessage);
    }

    return isValid;
}

// Show error message
function showErrorMessage(field, message) {
    const errorElement = document.createElement('span');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    errorElement.style.color = '#dc3545';
    errorElement.style.fontSize = '1.2em';
    errorElement.style.marginTop = '0.5em';
    errorElement.style.display = 'block';

    field.parentNode.appendChild(errorElement);
}

// Remove error message
function removeErrorMessage(field) {
    const errorMessage = field.parentNode.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification--${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 2em;
        right: 2em;
        background: ${type === 'success' ? '#28a745' : '#007bff'};
        color: white;
        padding: 1em 2em;
        border-radius: 0.5em;
        box-shadow: 0 0.5em 1em rgba(0,0,0,0.2);
        z-index: 1000;
        font-size: 1.4em;
        max-width: 25em;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Hide notification after 5 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}
