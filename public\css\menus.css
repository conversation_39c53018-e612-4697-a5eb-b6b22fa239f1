@keyframes toBottomFromTop {
    49% {
        -webkit-transform: translateY(-100%)
    }
    50% {
        opacity: 0;
        -webkit-transform: translateY(100%)
    }
    51% {
        opacity: 1
    }
}
/** menu search */
@keyframes translatey-keyframe{
    0% {
      transform: translateY(-100%);
    }
    100% {
      transform: translateY(0%);
    }
}
  
@keyframes btnTxtMarqueeX {
    100% {
        transform: translateX(-200%)
    }
}
@keyframes btnTxtMarqueeY {
    100% {
        transform: translateY(-200%)
    }
}

@keyframes stickySlideDown {
    0% {
        transform: translateY(-100%);
    }

    to {
        transform: translateY(0);
    }
}

.header {
    position: relative;
    z-index: 99;
}
/* ----------- HEADER TOP ----------- */
.header-info-bar{
    background-color: var(--light-dark);
    font-size: var(--10px);
    padding: 1em 0;
    
}

.header-top-wrapper{
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.header-logo{
    width: 14.5em;
    height: 4.2em;
}

.header-logo__img{
    width: 100%;
    height: 100%;
}

.header-support{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--10px);
    gap:1em;
}

.header-support__icon{
    display: flex;
    align-items: center;
    justify-content: center;
    min-width:4.2em;
    height: 4.2em;
    width: 4.2em;
}
.header-support__icon>svg{
    width: 100%;
    height: 100%;
}
.header-support__content{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}
.header-support__content>h6{
    margin: 0;
    font-size: 1.5em;
}

.header-search{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    height: 4.2em;
    width: 50%;
}

.header-search__form{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--10px);
    height: 100%;
    width: 100%;
    background-color: var(--white);
    border-radius: 1.2em;
}

.search-category{
    width: 30%;    
    height: 4.2em;    
    position: relative;
}

.search-category::after{
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 0.1em;
    height: 2em;
    background-color: rgba(187,187,187,.68);
}

.search-category__title{
    font-size: 1.4em;
    width: 100%;
    height: 100%;
    position: static;
    background-color: transparent;    
    display: flex;
    align-items: center;
    padding-left: 2em;
}

.search-category__title::after{
    border: 0;
    content: "\ea4e";
    font-size: 1em;
    margin-left: 0.3em;
    margin-top: 0;
    opacity: .5;
    vertical-align: middle;
    font-weight: 900;
    font-family: remixicon!important;
}

.search-category__dropdown.show{
    display: block;
}

.search-category__dropdown{
    display: none;
    background-color: var(--white);
    font-size: 1em;
    padding: 1em 0.7em;
    position: absolute;
    inset: 0 auto auto 0;
    transform: translate3d(0, 4.1em, 0);
    min-width: 16em;
    max-height: 30em;
    overflow-y: scroll;
    border-radius: 0.5em;
    box-shadow: 0 0 5em 0 rgba(0,0,0,.1);
    z-index: 9999;
}

.search-category__dropdown > li{
    display: block;
    width: 100%;
    font-size: 1.4em;
    padding: 0.3em 0.5em 0.3em 1em;
    margin-bottom: 0.2em;
    transition: 0.3s;
}

.search-category__dropdown > li.active, .search-category__dropdown > li:hover{
    background-color: var(--light);
    color: var(--primary);
    cursor: pointer;
    font-weight: 700;
}

.search-category__dropdown > li.group{
    font-weight: 700;
    font-size: 1.5em;
    padding-left: 0.3em;
}

.search-category__dropdown > li.group:hover{
    color: var(--black);
    background-color: var(--white);
}

.header-search__input{  
    border: 0;  
    outline: none;
    background-color: transparent;
    font-size: 1.4em;
    width: 100%;
    height: 3em;
    padding: 0 1em;
}
.header-search__btn{
    font-size: var(--10px);
    width: 6em;
    height: 4.2em;
    outline: none;
    border: 0;
    background-color: transparent;
    cursor: pointer;   
    border-radius: 50%;
}

.header-search__btn >i{
    font-size: 2.2em;
    transition: 0.3s;
}
.header-search__btn:hover >i{
    font-size: 2.5em;
    color: var(--primary);
}

/* --- navbar nav ---- */
.header-wrapper{
    background-color: var(--white);   
    box-shadow: var(--shadow-1);
}
.header-navbars{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;    
}
.header-wrapper.maybi-sticky{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    animation: stickySlideDown 0.65s cubic-bezier(0.23, 1, 0.32, 1) both;
}

.cate-menus__wrapper {
    display: flex;
    align-items: center;
    gap: 1em;
    position: relative;
}

.group-icons-menus{
    display: flex;
    align-items: center;
    gap: 1em;
}

.group-icons-menus > .m_extra_nav__item{
    width: 100%;
}

.maybi__logo {    
    width: 14em;
    height: 3.9em; 
    display: none;
    transition: all 0.5s ease;  
}

.maybi__logo__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.maybi-sticky .maybi__logo {
   display: block;    
}

.maybi-sticky .cate-menus {
    width: 16em;
    transition: all 0.3s ease;
}

.maybi-sticky .cate-menus__label >* {
    font-size: 1.6em;
}

/* --- browser category --- */
.cate-menus{
    font-size: var(--10px);    
    width: 30em;
    position:inherit;
    padding: 0.5em 0;
    height: 5.5em;
    display: flex;
    gap: 0.5em;
    align-items: center;
}


.cate-menus__label{
    position: relative;
    width: 100%;  
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1em;
    cursor: pointer;
    background-color: var(--light-dark);    
    border-radius: 1em;
    padding: 0 1em;
    transition: transform 0.3s ease;
}

.cate-menus__label >*{
    font-size: 1.8em;
}

.cate-menus__label:hover > .cate-menus__icon{
    transform: translateY(-50%) rotate(180deg);
}
.cate-menus__label:hover > span{
    font-weight: 700;
}

.cate-menus__icon{
    position: absolute;
    right: 0.5em;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.8em;
    transition: transform 0.3s ease;
}

.cate-menus__items{
    display: none;
    background-color: var(--white);
    font-size: 1em;   
    position: absolute;
    top: 5.6em;
    left: 0;
    right: 0;
    width: 30em;
    min-width:  30em;   
    border-radius: 0.5em;
    box-shadow: 0 0 5em 0 rgba(0,0,0,.1); 
    transition: 0.5s;
    z-index: 9999;
}
.cate-menus:hover > .cate-menus__items{
    display: block; 
}

.cate-menus__nav{
    padding-top: 2em;
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

.cate-menus__nav > li{
    transition: all 0.5s ease 0s;
	z-index:1;    
    margin-bottom: 0.2em;	 
}

.cate-menus__nav >li>a::before{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--gradient1);
    transition: -webkit-transform 0.4s ease;
    transition: transform 0.4s ease;
    transition: transform 0.4s ease,-webkit-transform 0.4s ease;
    -webkit-transform: scale(0, 1);
    -ms-transform: scale(0, 1);
    transform: scale(0, 1);
    -webkit-transform-origin: right center;
    -ms-transform-origin: right center;
    transform-origin: right center;
}

.cate-menus__nav > li>a:hover:before{	
	-webkit-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    transform: scale(1, 1);
    -webkit-transform-origin: left center;
    -ms-transform-origin: left center;
    transform-origin: left center;
}

.cate-menus__nav > li >a{
    position: relative;
    font-size: 1.6em;
    display: flex;
    align-items: center;
    gap: 0.5em;
    font-weight: 500;
    padding: 0.5em 0.5em;
    transition: all 0.5s ease 0s;
}

.cate-menus__nav > li:hover>a{
    padding-left: 0.8em;
}

.cate-menus__nav > li:hover>a>span{
    font-weight: 700;
}

.mega-menu__icon{
    position: absolute;
    right: 0.5em;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1em;
    transition: transform 0.3s ease;
    width: 1.2em;
    height: 1.2em;
    background-color: rgba(var(--rgb-black), 0.7);
    border-radius: 0.2em;
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
}

.mega-menus__nav{
    font-size: var(--10px);
    visibility: hidden;
    opacity: 0;
    transition: all 0.5s;
    position: absolute;
    top: 0;
    left: 100%;
    right: 0;
    width: 90em;
    min-height: 100%;
    padding: 2em;
    border-radius: 0.5em;
    box-shadow: 0 2em 4em rgba(0, 0, 0, .05);
    background-color: var(--white);
}

.mega-menus__nav::after{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0.15em;
    border-radius: 0.5em;
    background: linear-gradient(180deg, rgba(70, 193, 178, 0.8) 0%, rgba(254, 224, 0, 0.2) 63.5%, rgba(254, 224, 0, 0) 100%);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;    
}

.mega-menus:hover .mega-menus__nav{
    visibility: visible;
    opacity: 1;
}

.mega-menus__title{
    font-size: 1.6em;
    padding-bottom: 0.5em;
    margin-bottom: 2em;
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.7em;
}

.mega-menus__title::before{
    content: "";
    position: absolute;
    top: 100%;
    left: 0;
    width: 7em;
    height: 0.1em;
    background-color: rgba(var(--rgb-black), 0.2);
}

.mega-menus__title>i{
    color: var(--primary);
}

.mega-sales{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 2em;
}

.mega-sales__item{
    width: 31.7%;
    height: 7em;
    border-radius: 1em;    
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    padding: 1em;
    display: flex;
    align-items: center;
    gap: 3em;
    box-shadow: 0 2em 4em rgba(0, 0, 0, .05);
    transition: 0.3s linear;
}

.mega-sales__thumb{
    width: 5em;
    height: 5em; 
    position: relative;  
}
.mega-sales__thumb::after{
    content: "";
    position: absolute;
    top: 50%;
    left: 6.5em;
    width: 0.1em;
    height: 2em;
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    transform: translateY(-50%);
}

.mega-sales__img{
    width: 100%;
    height: 100%;
    border-radius: 0.5em;
    object-position: center;
    object-fit: cover;
}

.mega-sales__title{
    color: rgba(var(--rgb-black),0.7);
    width: calc(100% - 6em);
    max-height: 3.5em;
    font-size: 1.4em;
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    transition: 0.3s;
}
.mega-sales__item:hover{
    transform: translateY(-0.2em);
    box-shadow: 0 0.4em 0.4em rgb(14 55 54 / 15%);
}

.mega-sales__item:hover > .mega-sales__title{
    color: var(--primary);
}

/* -- mega vouchers */
.mega-vouchers{
    font-size: var(--10px);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30em, 40em));
    gap: 2em;
    align-items: center;
    justify-content: center;
}

.voucher-item{
    font-size: var(--10px);
    display: flex;
    height: 12em;
    border-top-right-radius: 1em;
    border-bottom-right-radius: 1em;
    transition: 0.3s linear;
}

.voucher-item:hover{
    transform: translateY(-0.1em);
    box-shadow: 0 0.4em 0.4em rgb(14 55 54 / 15%);
}

.voucher-item__thumb{
    position: relative;
    width: 30%;
    height: 100%;
    background-image: url(../images/bg-vouchers.webp);
    background-size: cover;
    background-position: left;
    background-repeat: no-repeat;    
}

.voucher-item__thumb::after{
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5.5em;
    height: 5.5em;
    border-radius: 50%;
    background-color: var(--white);
    transform: translate(-50%, -50%);
}

.mega-vouchers__img{
    width: 4em;   
    height: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.voucher-item__tag{
    position: absolute;
    left: 0;
    top: 0.3em;
    font-size: 0.9em;
    padding: 0.15em 0.3em;
    border-radius: 0.2em;
    background-color: rgb(var(--rgb-orange));
    color: var(--white);
}

.voucher-item__des{
    position: absolute;
    left: 0;
    bottom: 0.3em;
    font-size: 0.9em;
    width: 100%;
    padding: 0 0.8em;
    height: 2.2em;
    line-height: 1.1;
    color: var(--white);
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.voucher-item__content{
    font-size: var(--10px);
    height: 100%;
    width: 70%;   
    border-top: 0.1em solid rgba(var(--rgb-black), 0.2);
    border-right: 0.1em solid rgba(var(--rgb-black), 0.2);
    border-bottom: 0.1em solid rgba(var(--rgb-black), 0.2);
    border-top-right-radius: 1em;
    border-bottom-right-radius: 1em;
}
.voucher-item__wrapper{
    padding: 1.2em;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.voucher-item__title{
    font-size: 1.6em;
    font-weight: 700;
    margin-bottom: 0.4em;
}
.voucher-item__desc{
    color: rgba(var(--rgb-black), 0.6);
    font-size: 1.2em;
    margin-bottom: 0.7em;
    max-height: 3em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.voucher-item__info{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--10px);
}

.voucher-item__time{
    display: flex;
    align-items: center;
    gap: 0.3em;
    color: rgba(var(--rgb-black), 0.8);
}

.voucher-item__time>strong{
    font-weight: 500;
}

.voucher-item__btn{
    font-size: 1.2em;
    padding:0.3em 1.7em;
    border-radius: 1em;
    background: linear-gradient(234.87deg,#f18 46.25%,#fc4c4f 81.16%);
    color: var(--white);
    font-weight: 500;
    position: relative;
    overflow:hidden;
    transition: 0.3s;
    z-index: 1;
}

.voucher-item__btn::before{
   position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 50%;
    opacity:0 ;
    z-index: -1;
    content: '';
    background-color:var(--primary);
    -webkit-transition: all 0.4s linear 0s;
    -o-transition: all 0.4s linear 0s;
    transition: all 0.4s linear 0s;
    -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -ms-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    transform: translateX(-50%) translateY(-50%) rotate(45deg);
}
.voucher-item__btn:hover:before{
	height: 500%;
    opacity: 1;
}

.voucher-item__btn:hover{
    color: var(--light-dark);
}

.mega-items{
    font-size: var(--10px);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(15em, 15em));
    gap: 3em 2em;
    align-items: center;
}

.mega-item{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1.5em;   
}
.mega-item:hover > .mega-item__thumb{
    transform: scale(1.05);
    box-shadow: 0 0.6em 0.6em rgb(14 55 54 / 25%);
}

.mega-item__thumb{
    width: 10em;
    height: 10em;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient1);
    box-shadow: 0 0.4em 0.4em rgb(14 55 54 / 15%);
    transition: transform 0.3s ease, box-shadow 0.3s ease; 
}

.mega-item__icon{
    font-size: 5em;
    font-weight: 500;
}

.mega-menu__img{
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-position: center;
    object-fit: cover;
}

.mega-item__title{
    font-size: 1.3em;
    font-weight: 700;
    text-align: center;
}
.mega-other{
   background-color: rgba(var(--rgb-black), 0.1); 
}

.mega-brands{
    font-size: var(--10px);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(15em, 15em));
    gap: 2.5em;
    align-items: center;
}

.mega-brand{
    width: 15em;
    height: 8em;
    background: var(--gradient1);
    border-radius: 1em;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}
.mega-brand:hover >.mega-brand__thumb{
    animation: toBottomFromTop .5s forwards;
}

.mega-brand__thumb{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
}

.mega-brand__icon{
    font-size: 3em;
}

.mega-brand__img{
    width: 8em;
    max-height: 7.5em;
    object-fit: cover;
    object-position: center;;
}

.mega-brand__title{
    font-size: 1.8em;
    font-weight: 700;
}   
/* -------------- menus nav ------------------------ */
.menus-nav{
    font-size: var(--10px);
    height: 5.5em;
    display: flex;
    align-items: center;    
}
.menus-main{
    display: flex;
    align-items: center;
    gap: 1.8em;
    height: 100%;
}

.menu-item{
    font-size: 1.5em;
    font-weight: 600;
    color: rgba(var(--rgb-black),0.8);
    padding-left: 0.2em;
    padding-right: 0.2em;
    height: 100%;
}
.menu-item > a{
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.menu-item__link{
    display: flex;
    gap: 0.5em;
}

.menu-item__icon{
    color: rgba(var(--rgb-black),0.8);
    transition: 0.3s;
}

.menu-item:hover .menu-item__icon{
    transform: rotate(-180deg);
}

.menu-item.active{
    color: var(--primary);
    border-bottom: 1px solid var(--primary);
    font-weight: 700;
}

.menu-item > a::after {
    position: absolute;
    content: "";
    left: auto;
    bottom: 0;
    background-color: var(--primary);
    width: 0;
    height: 0.05em;
    transition: var(--transition);
    right: 0;
}
.menu-item:hover > a::after{
    width: 100%;
    left: 0;
    right: auto;
}

/* --> menu-dropdown */
.menu-item--dropdown:hover > .menu-dropdown{
    visibility: visible;
    opacity: 1;
    margin-top: 0;
}

.menu-dropdown{
    font-size: var(--10px);
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    width: 100%;    
    visibility: hidden;
    opacity: 0;
    margin-top: 1em;   
    transition: all .5s;
    box-shadow: var(--shadow-1);
}
.menu-wrapper{
    padding: 2em;
    width: 100%;
    margin: auto;    
    background-color: var(--white);
    border-bottom-left-radius: 1em;
    border-bottom-right-radius: 1em;
    display: grid;
    grid-template-columns: repeat(4, auto);
    gap: 2em;
}

.submenu-group{
    display: flex;
    flex-direction: column;
    gap: 2em;
}

.submenu-group__title{
    font-size: 1.8em;
    font-weight: 700;
    display:flex;
    align-items: center;
    gap: 0.2em;
}

.submenu-group__icon{
    width: 2.5em;
    height: 1.5em;
    fill: var(--black);
    transition: var(--transition);
    transform: translateX(0);
}

.submenu-group__title:hover{
    color: var(--primary);
}
.submenu-group__title:hover >.submenu-group__icon{
    transform: translateX(0.5em);
    fill: var(--primary);
}

.submenu-list{
    display: flex;
    flex-direction: column;
    gap: 1.6em;
}

.submenu-list2{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2em;
}

.submenu-item{
    font-size: var(--10px); 
    position: relative;
}

.submenu-item:not(:last-child)::before{
    content: "";
    position: absolute;
    top: calc(100% + 0.8em);
    left: 25%;
    background-color: rgba(var(--rgb-black), 0.2);
    width: 3em;
    height: 0.05em;
}

.submenu-item__thumb{
    display: flex;
    align-items: center;
    gap: 1em;
}

.submenu-item__img{
    width: 4em;
    height: 4em;
    border-radius: 50%;
    object-fit: cover;
    object-position: center;   
    transition: var(--transition);
}
.submenu-item__title{
    display: flex;
    flex-direction: column;
    gap: 0.2em;
}
.submenu-item__title>span{
    font-size: 1.6em;
    font-weight: 500;
    color: rgba(var(--rgb-black),0.8);
}

.submenu-item__title>time{
    font-size: 1em;
    color: rgba(var(--rgb-black),0.5);
    padding-left: 1em;
}

.submenu-item__thumb:hover > .submenu-item__img{
    transform: scale(1.1);
}
.submenu-item__thumb:hover > .submenu-item__title>span{
    color: var(--primary);
}



/* -------------- header right --------------------- */
.header-right{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1em;
}

.search-link, .wishlist-link, .cart-btn{
    font-size: 2em;
    position: relative;
    width: 1.8em;
    height: 1.8em;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.search-link::after, .wishlist-link::after, .cart-btn::after{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;   
    border-radius: 50%;
    background-color: rgba(var(--rgb-black),0.1);
    transform: scale(0.8);
    opacity: 0;
    transition: 0.3s;
}
.search-link:hover::after, .wishlist-link:hover::after, .cart-btn:hover::after{
    opacity: 1;
    transform: scale(1);
}

.cart-btn__count{
    font-size: 0.5em;
    position: absolute;
    top: 0.3em;
    right: 0.2em;
    width: 1.5em;
    height: 1.5em;
    border-radius: 50%;
    background-color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);    
}

.cart-btn__count.hidden{
    visibility: hidden;
    opacity: 0;
}

/* --- search box dropdown --- */
.search__dropdown{
    font-size: var(--10px);
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border-top: 0.1em solid #f1f0f3;
    box-shadow: 0 0.4em 1.6em rgba(8, 10, 18, 0.1);
    visibility: hidden;
    opacity: 0;   
    transform: translateY(-1.5em);
    transition: transform 0.3s ease, opacity 0.3s ease, visibility 0s 0.3s;
}
.search__dropdown.active {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
    transition: transform 0.3s ease, opacity 0.3s ease;
    z-index: 999;
}

.search__dropdown__wrapper{
    font-size: var(--10px);
    max-width: 90em;
    margin: 1.6em auto;
    padding: 0 1.6em;
    font-weight: 400;
}
.search__dropdown__form{
    margin: 3em 0;
}
.search__dropdown__form form{ 
    display: flex;
}
.search__dropdown__form form input{ 
    width: 100%;
    font-size: 1.6em;
    font-weight: 400;
    height: 3.125em;
    line-height: 3em;
    margin: 0 0.75em 0 0;
    padding: 0 1em;
    border-radius: 0.3em;
    border: 0.1em solid rgba(var(--rgb-black),0.2);
    transition: 0.3s;
}

.search__dropdown__form form input::placeholder{
    color: rgba(var(--rgb-black),0.5);
}

.search__dropdown__form form button{
    display: inline-block;
    font-size: 1.6em;
    width: 9em;
    height: 3.125em;
    line-height: 3em;
    background-size: 300% 100%;
    background-image: linear-gradient(to right, var(--light-dark), var(--light-dark), #faeeee, var(--light-dark));
    max-width: 100%;
    outline: none;
    border:none;
    border-radius: 0.45em;
}
.search__dropdown__form form button:hover{
    background-color: inherit;
    background-position: 100% 0;
    transition: all 0.4s ease-in-out;
}

.search__dropdown__result{
    margin-top: 3em;
    padding-top:3em;
    border-top: 0.1em dashed rgba(var(--rgb-black),0.1);
}
.search__title{
    margin:auto;
    font-size: 1.6em;
    text-transform: uppercase;
    padding:0.8em 1.7em;
    color:var(--red);
    background-color: rgba(var(--rgb-black),0.1);
    border-radius: 1.9em;
    width: fit-content;
}
.search__list{
    font-size: var(--10px);
    margin-top: 2em;
    margin-bottom: 5em;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 2em;
}

.search__list__keyword{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2em;
}
.search__list__keyword a, .search__keyword__blank{
    font-size: 1.4em;
    padding: 0 1.2em;
    border-radius: 2em;
    background: rgba(var(--rgb-black),0.05);
    color: var(--color-body);
    box-shadow: var(--shadow-1);
    display: inline-block;
    height: 2.5em;
    line-height: 2.5em;
    min-width: 5em;
    transition: var(--transition);
}
.search__list__keyword a:hover{
    background: rgba(var(--rgb-black),0.1);
    color: var(--primary);
    transform: scale(1.1);
}

.search__keyword__blank {
    box-shadow: 1.9em 2em 3.4em 0 rgba(164, 160, 196, 0.19);
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #dddbdb 100%);
    pointer-events: none;
}

/* --- event search dropdown --- */
.search-opened body::before {
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(1.5em);
    opacity: 1;
    z-index: 99;
    pointer-events: auto;
}
.search-link.open > .ri-search-line::before{
    content: "\eb98";
}
.search-link.open::after{
    opacity: 1;
    transform: scale(1);
}

.menu-mobile-bar{
    display: none;
    font-size: var(--10px);
    width: 6em;
    height: 6em;
    padding: 1em;
    background-color: var(--secondary);
    position: relative;
    transform: rotate(0deg);
    cursor: pointer;
    transition: all 0.5s ease-in-out;
    border: none;
    outline: none; 
    z-index: 999;
}

.menu-mobile-bar > span {
    display: block;
    position: absolute;
    height: 0.3em;
    width: 100%;
    border-radius: 0.1em;
    opacity: 1;
    background: var(--white);    
    transform: rotate(0deg);
    transition: .25s ease-in-out;
}

.menu-mobile-bar>span:first-child {
    top: 1.5em;
    width: 3em;
}

.menu-mobile-bar>span:nth-child(2) {
    top: 2.8em;
    width: 4.2em;
}
.menu-mobile-bar>span:nth-child(3) {
    top: 4.3em;
    width: 3em;
}
.menu-mobile-bar.open>span:first-child {
    top: 3em;
    width: 4em;
    transform: rotate(135deg);
}
.menu-mobile-bar.open>span:nth-child(2) {
    opacity: 0;
    left: -6em;
}
.menu-mobile-bar.open>span:nth-child(3) {
    top: 3em;
    width: 4em;
    transform: rotate(-135deg);
}
/** Menus mobile */
.menus-mobile{
    font-size: var(--10px);
    position: fixed;
    width: 100%;
    left: -100%;
    height: 100svh;
    overflow-y: scroll;
    top: 0;
    margin: 0;
    background-color: var(--white);
    z-index: 9999;
    transition: all 0.5s ease-in-out;
    padding: 0.5em 1em 0 1em;
}

.menus-mobile.show{
    left: 0;
}

.m-logo{
    display: block;
    width: 28em;
    height: 8em;
}
.m-logo__img{  
    width: 100%;
    height: 100%;
}
.m-menus{
    padding-top: 3em;  
    padding-left: 1.5em;  
    padding-right: 0.5em;
    color: var(--secondary);    
}

.m-menus__item{
    font-size: 2.5em;  
    font-weight: 600;    
    position: relative;
}

.m-menus__link{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.5em;
    border-bottom: 1px solid rgba(var(--rgb-black), 0.1); 
}

.m-menus__item:hover{
    color: var(--primary);
}

.m-menus__icon{
    font-size: 1rem;
    width: 2.2em;
    height: 2.2em;
    display: flex;
    align-self: center;
    justify-content: center;
    background-color: rgb(var(--rgb-black));
}
.m-menus__icon>svg{
    width: 1.5em;
    fill: var(--white);
    transition: all 0.3s ease;
}

.m-menus__sub{
    display: block;
    font-size: 1rem;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s linear;
    height: 0;
    overflow: hidden;
}

.m-mega-sales{
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5em;
    width: 100%;    
    padding: 1em 1em;
}

.m-mega-sales__item{
    width:100%;
    border-radius: 1em;
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    padding: 1em;
    display: flex;
    align-items: center;
    gap: 3em;
    box-shadow: 0 2em 4em rgba(0, 0, 0, .05);
    transition: 0.3s linear;
}

.has-show.show > .m-menus__sub{
    display: block;
    opacity: 1;
    visibility: visible;
    height: auto;
}

.m-menus__item.show .m-menus__icon>svg{
    transform: rotate(90deg);
}

.m-menus__items{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(6.2em, 1fr));
    gap: 1.5em 0.5em;
    width: 100%;    
    padding: 1em;
}

.m-menus__items > .mega-item{
    justify-content:flex-start;
}

.m-menus__items .mega-item__thumb {
    width: 6em;
    height: 6em;   
}

.m-menus__items .mega-item__title{
    font-size: 1em;
    color: var(--black);
}

.m-menus__support{
    font-size: var(--10px);
    padding: 4em 1em 2em 1em;
}
.m-menus__title{
    font-size: 2.5em;
    font-weight: 600;
    color: var(--primary);
}
.m-menus__list{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5em;
    width: 100%;
    margin-bottom: 3em;
}
.m-menus-sup{
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 2em;
}
.m-menus-sup__icon{
    width: 5.5em;
    height: 5.5em;
    padding: 1em;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient1);
    fill: var(--black);
    border-radius: 50%;
}
.m-menus-sup__text{
    font-size: 2em;
    font-weight: 400;
}
.m-menus__support__header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2.5em;
    position: relative;
}

.m-menus__support__header::before{
    content: "";
    position: absolute;
    top: 100%;
    left: 0;
    width: 7em;
    height: 0.1em;
    background-color: rgba(var(--rgb-black), 0.5);
}

.m-menus__social{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.2em;
}

.m-menus__social__item{
    width: 6em;
    height: 6em;
    padding: 1em;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(var(--rgb-black), 0.1);    
    transition: all 0.3s ease;
}

.m-menus__social__icon{
    fill: rgba(var(--rgb-black), 0.75);
}
.m-menus__copyright{
    font-size: 2em;
    text-align: center;
    color:  rgba(var(--rgb-black), 0.7);
    margin-bottom: 0.2em;
}
.m-menus__payment{
    font-size: 2em;
    text-align: center;   
    color: rgba(var(--rgb-black), 0.7); 
}
.m-menus__payment__img{
    width: 60%;
}

/* Mobile Header with Exit Button */
.m-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5em 0;
    border-bottom: 1px solid rgba(var(--rgb-black), 0.1);
    margin-bottom: 1em;
}

/* Mobile Close Button in Header */
.m-close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3em;
    height: 3em;
    background: none;
    border: none;
    border-radius: 50%;
    color: var(--secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: rgba(var(--rgb-black), 0.05);
}

.m-close-btn:hover {
    background-color: rgba(var(--rgb-black), 0.1);
    color: var(--primary);
    transform: scale(1.05);
}

.m-close-btn:active {
    transform: scale(0.95);
}

.m-close-btn__icon {
    width: 2.2em;
    height: 2.2em;
    fill: currentColor;
    transition: transform 0.3s ease;
}

.m-close-btn:hover .m-close-btn__icon {
    transform: rotate(90deg);
}
.menus-mobile.show .m-exit-btn{
    display: flex;
}
.m-exit-btn {
    display: none;
    align-items: center;
    gap: 0.5em;
    background: none;
    border: none;
    padding: 0.8em 1.2em;
    border-radius: 0.5em;
    color: var(--secondary);
    font-size: 1.4em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: rgba(var(--rgb-black), 0.05);
    position: fixed;
    top: 60vh;
    right: 1.5em;
    flex-direction: column;
    z-index: 99;
}

.m-exit-btn:hover {
    background-color: rgba(var(--rgb-black), 0.1);
    color: var(--primary);
}

.m-exit-btn:active {
    transform: scale(0.98);
}

.m-exit-btn__icon {
    width: 1.8em;
    height: 1.8em;
    fill: currentColor;
    transition: transform 0.3s ease;
}

.m-exit-btn:hover .m-exit-btn__icon {
    transform: translateX(-0.2em);
}

.m-exit-btn__text {
    font-size: 1em;
    font-weight: 600;
}

/* Hide exit button on desktop */
@media (min-width: 768px) {
    .m-exit-btn {
        display: none;
    }
}