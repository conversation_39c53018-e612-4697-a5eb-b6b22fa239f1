const quickViewModal = (function () {
  let modal, closeBtn, quickViewSwiper;

  function initModal() {
    modal = document.getElementById("product-modal");
    closeBtn = document.getElementById("modal-close");

    if (!modal || !closeBtn) return;

    document.querySelectorAll(".btn-quick-view").forEach((btn) => {
      btn.addEventListener("click", function (e) {
        e.preventDefault();
        const productElement = this.closest('article');
        loadProductData(productElement);
        openModal();
      });
    });

    closeBtn.addEventListener("click", closeModal);
    modal.addEventListener("click", (e) => {
      if (e.target.classList.contains("product-modal__overlay")) {
        closeModal();
      }
    });
  }

  function loadProductData(productElement) {
    if (!productElement || !modal) return;

    // Extract product data from the product element
    const productData = extractProductData(productElement);

    // Update modal content with product data
    updateModalContent(productData);
  }

  function extractProductData(productElement) {
    const data = {};

    // Get product images
    const mainImg = productElement.querySelector('.product__img');
    const hoverImg = productElement.querySelector('.product__img--hover');
    data.images = [];
    if (mainImg) data.images.push(mainImg.src);
    if (hoverImg) data.images.push(hoverImg.src);

    // Get product title and link
    const titleLink = productElement.querySelector('.product__title a');
    data.title = titleLink ? titleLink.textContent.trim() : '';
    data.link = titleLink ? titleLink.href : '#';

    // Get product prices
    const salePrice = productElement.querySelector('.sale-price');
    const regularPrice = productElement.querySelector('.product-prices span:not(.sale-price)');
    data.salePrice = salePrice ? salePrice.textContent.trim() : '';
    data.regularPrice = regularPrice ? regularPrice.textContent.trim() : '';

    // Get product rating
    const ratingStars = productElement.querySelectorAll('.mb-start-fill').length;
    const soldCount = productElement.querySelector('.product-rating__selled');
    data.rating = ratingStars;
    data.soldCount = soldCount ? soldCount.textContent.trim() : '';

    // Get product tags
    data.tags = [];
    const tagElements = productElement.querySelectorAll('.product-tag__item span');
    tagElements.forEach(tag => {
      data.tags.push({
        text: tag.textContent.trim(),
        type: tag.parentElement.classList.contains('product-tag__item--sale') ? 'sale' :
          tag.parentElement.classList.contains('product-tag__item--new') ? 'new' : 'hot'
      });
    });

    // Get product colors
    data.colors = [];
    const colorElements = productElement.querySelectorAll('.product-color');
    colorElements.forEach(colorEl => {
      if (!colorEl.classList.contains('product-color--more')) {
        const colorSpan = colorEl.querySelector('.product-color__item');
        const img = colorEl.getAttribute('data-img');
        if (colorSpan) {
          data.colors.push({
            color: colorSpan.style.backgroundColor,
            image: img,
            active: colorEl.classList.contains('active')
          });
        }
      }
    });

    // Get product sizes with color data
    data.sizes = [];
    const sizeElements = productElement.querySelectorAll('.product-size__item');
    sizeElements.forEach(sizeEl => {
      const sizeText = sizeEl.textContent.trim();
      const colorData = sizeEl.getAttribute('data-color');
      data.sizes.push({
        size: sizeText,
        colorData: colorData ? JSON.parse(colorData) : [],
        active: sizeEl.classList.contains('product-size--active')
      });
    });

    // Get product ID from quick view button
    const quickViewBtn = productElement.querySelector('.btn-quick-view[data-product-id]');
    data.productId = quickViewBtn ? quickViewBtn.getAttribute('data-product-id') : '1';

    // Get additional product data from data attributes
    data.description = productElement.getAttribute('data-description') || '';
    data.productCode = productElement.getAttribute('data-product-code') || '';
    data.categories = productElement.getAttribute('data-categories') || '';
    data.productTags = productElement.getAttribute('data-tags') || '';

    return data;
  }

  function updateModalContent(data) {
    // Update product images
    updateProductImages(data.images);

    // Update product title
    const titleElement = modal.querySelector('.product-modal__title a');
    if (titleElement) {
      titleElement.textContent = data.title;
      titleElement.href = data.link;
    }

    // Update product tags
    updateProductTags(data.tags);

    // Update product prices
    updateProductPrices(data.salePrice, data.regularPrice);

    // Update product rating
    updateProductRating(data.rating, data.soldCount);

    // Update product sizes first
    updateProductSizes(data.sizes);

    // Update product colors based on active size
    updateColorsBasedOnActiveSize();

    // Update product ID in add to cart button
    const addToCartBtn = modal.querySelector('.btn_add_to_cart');
    if (addToCartBtn) {
      addToCartBtn.setAttribute('data-product-id', data.productId);
    }

    // Update product description
    updateProductDescription(data.description);

    // Update product info (code, categories, tags)
    updateProductInfo(data.productCode, data.categories, data.productTags);

    // Ensure modal has product class for cart compatibility
    ensureModalCartCompatibility();

    // Format modal info after updating content
    setTimeout(() => formatModalInfo(), 100);
  }

  function openModal() {
    modal.classList.remove("product-modal--hidden");
    document.body.classList.add("modal-open");

    // Initialize interactions after modal is visible
    setTimeout(() => {
      initModalInteractions();
    }, 50);
  }

  function closeModal() {
    modal.classList.add("product-modal--hidden");
    document.body.classList.remove("modal-open");
  }

  function updateProductImages(images) {
    if (!images || images.length === 0) return;

    // Update thumbnails
    const thumbnailContainer = modal.querySelector('.product-thumbnails');
    if (thumbnailContainer) {
      thumbnailContainer.innerHTML = '';
      images.forEach((imgSrc, index) => {
        const img = document.createElement('img');
        img.src = imgSrc;
        img.setAttribute('data-slide', index);
        if (index === 0) img.classList.add('active');
        thumbnailContainer.appendChild(img);
      });
    }

    // Update swiper slides
    const swiperWrapper = modal.querySelector('.swiper-wrapper');
    if (swiperWrapper) {
      swiperWrapper.innerHTML = '';
      images.forEach((imgSrc) => {
        const slide = document.createElement('div');
        slide.className = 'swiper-slide';
        slide.style.width = '100%';
        slide.style.marginRight = '1.2em';
        slide.innerHTML = `
          <div class="product__box product__style wow fadeInUp will-animate">
            <div class="product__thumb-img">
              <img src="${imgSrc}" alt="Product Image" />
            </div>
          </div>
        `;
        swiperWrapper.appendChild(slide);
      });
    }

    // Update hidden direct image for cart compatibility
    const directImg = modal.querySelector('.product-modal__content .product__img[style*="display: none"]');
    if (directImg && images.length > 0) {
      directImg.src = images[0]; // Set to first image
    }

    // Reinitialize swiper after updating slides
    if (quickViewSwiper) {
      quickViewSwiper.destroy(true, true);
    }
    initQuickViewSwiper();
  }

  function updateProductTags(tags) {
    const tagContainer = modal.querySelector('.product-modal__content-box');
    if (!tagContainer) return;

    tagContainer.innerHTML = '';
    tags.forEach(tag => {
      const tagElement = document.createElement('span');
      tagElement.className = 'product-modal__label';

      if (tag.type === 'sale') {
        tagElement.classList.add('product-modal__sale-percent');
      } else if (tag.type === 'hot') {
        tagElement.classList.add('product-modal__sale-hot');
      }

      tagElement.textContent = tag.text;
      tagContainer.appendChild(tagElement);
    });
  }

  function updateProductPrices(salePrice, regularPrice) {
    const priceContainer = modal.querySelector('.product-modal__price');
    if (!priceContainer) return;

    let priceHTML = '';
    if (salePrice) {
      priceHTML = `<span class="sale-price">${salePrice}</span>`;
      if (regularPrice && regularPrice !== salePrice) {
        priceHTML += ` <del class="product-modal__old-price">${regularPrice}</del>`;
      }
    } else if (regularPrice) {
      priceHTML = `<span class="sale-price">${regularPrice}</span>`;
    }

    priceContainer.innerHTML = priceHTML;
  }

  function updateProductRating(rating, soldCount) {
    // Update rating stars
    const ratingContainer = modal.querySelector('.mb-rating');
    if (ratingContainer) {
      ratingContainer.innerHTML = '';
      for (let i = 1; i <= 5; i++) {
        const star = document.createElement('svg');
        star.className = i <= rating ? 'mb-start-fill' : 'mb-start';

        const use = document.createElement('use');
        use.setAttribute('href', i <= rating ? '#icon-star-active' : '#icon-star-fill');
        star.appendChild(use);

        ratingContainer.appendChild(star);
      }
    }

    // Update sold count
    const soldElement = modal.querySelector('.product-rating__selled');
    if (soldElement && soldCount) {
      soldElement.textContent = soldCount;
    }
  }

  function updateColorsBasedOnActiveSize() {
    // Find the active size and get its color data
    const activeSizeElement = modal.querySelector('.product-size__item.product-size--active');
    if (!activeSizeElement) return;

    const colorData = activeSizeElement.getAttribute('data-color');
    if (!colorData) return;

    try {
      const colors = JSON.parse(colorData);
      updateColorsForSize(colors);
    } catch (e) {
      console.error('Error parsing color data for active size:', e);
    }
  }



  function updateProductSizes(sizes) {
    const sizeContainer = modal.querySelector('.product-modal__options-left .product-size');
    if (!sizeContainer || !sizes.length) return;

    sizeContainer.innerHTML = '';
    sizes.forEach(sizeData => {
      const sizeElement = document.createElement('li');
      sizeElement.className = 'product-size__item maybi-hover';
      if (sizeData.active) {
        sizeElement.classList.add('product-size--active');
        sizeElement.classList.add('active'); // Add for compatibility
      }

      if (sizeData.colorData && sizeData.colorData.length > 0) {
        sizeElement.setAttribute('data-color', JSON.stringify(sizeData.colorData));
      }

      sizeElement.textContent = sizeData.size;
      sizeContainer.appendChild(sizeElement);
    });
  }

  function updateProductDescription(description) {
    const descElement = modal.querySelector('.product-modal__desc');
    if (descElement && description) {
      descElement.textContent = description;
    }
  }

  function updateProductInfo(productCode, categories, tags) {
    const infoContainer = modal.querySelector('.product-modal__info');
    if (!infoContainer) return;

    let infoHTML = '';

    // Product code
    if (productCode) {
      infoHTML += `<p><strong>Mã sản phẩm:</strong> ${productCode}</p>`;
    }

    // Categories
    if (categories) {
      const categoryList = categories.split(',').map(cat => cat.trim());
      const categoryLinks = categoryList.map(cat => `<a href="#" title="${cat}">${cat}</a>`).join('');
      infoHTML += `<p><strong>Danh mục:</strong> ${categoryLinks}</p>`;
    }

    // Tags
    if (tags) {
      const tagList = tags.split(',').map(tag => tag.trim());
      const tagLinks = tagList.map(tag => `<a href="#" title="${tag}">${tag}</a>`).join('');
      infoHTML += `<p><strong>Nhãn:</strong> ${tagLinks}</p>`;
    }

    infoContainer.innerHTML = infoHTML;
  }

  function ensureModalCartCompatibility() {
    // Ensure modal content has product class for cart_updater.js compatibility
    const modalContent = modal.querySelector('.product-modal__content');
    if (modalContent && !modalContent.classList.contains('product')) {
      modalContent.classList.add('product');
    }

    // Add product__title structure if missing
    const titleElement = modal.querySelector('.product-modal__title a');
    if (titleElement && !titleElement.closest('.product__title')) {
      const titleWrapper = document.createElement('div');
      titleWrapper.className = 'product__title';
      titleElement.parentNode.insertBefore(titleWrapper, titleElement);
      titleWrapper.appendChild(titleElement);
    }

    // Ensure quantity input has correct class
    const quantityInput = modal.querySelector('.product-modal__quantity-input');
    if (quantityInput && !quantityInput.classList.contains('quantity-input')) {
      quantityInput.classList.add('quantity-input');
    }

    // Ensure main product image has correct class and is accessible for cart
    const mainImg = modal.querySelector('.swiper-slide img');

    if (mainImg && modalContent) {
      // Add product__img class to swiper image
      if (!mainImg.classList.contains('product__img')) {
        mainImg.classList.add('product__img');
      }

      // Also create a direct .product__img element in modal content for cart compatibility
      let directImg = modalContent.querySelector('.product__img');
      if (!directImg) {
        directImg = document.createElement('img');
        directImg.className = 'product__img';
        directImg.style.display = 'none'; // Hidden, just for cart data extraction
        modalContent.appendChild(directImg);
      }

      // Keep the direct image src in sync with swiper image
      directImg.src = mainImg.src;
    }

    // Price is already handled by updateProductPrices function
  }

  function initModalInteractions() {
    // Only initialize if modal is visible to avoid conflicts
    if (!modal || modal.classList.contains('product-modal--hidden')) {
      return;
    }

    // Initialize size selection
    initSizeSelection();

    // Initialize color selection
    initColorSelection();

    // Initialize quantity controls
    initQuantityControls();
  }

  function initSizeSelection() {
    const sizeItems = modal.querySelectorAll('.product-size__item');
    sizeItems.forEach(item => {
      item.addEventListener('click', function () {
        // Remove active classes from all sizes
        sizeItems.forEach(size => {
          size.classList.remove('product-size--active');
          size.classList.remove('active');
        });

        // Add active classes to clicked size
        this.classList.add('product-size--active');
        this.classList.add('active');

        // Update colors based on size selection
        const colorData = this.getAttribute('data-color');
        if (colorData) {
          try {
            const colors = JSON.parse(colorData);
            updateColorsForSize(colors);
          } catch (e) {
            console.error('Error parsing color data:', e);
          }
        }
      });
    });
  }

  function initColorSelection() {
    const colorItems = modal.querySelectorAll('.product-color');
    colorItems.forEach(item => {
      item.addEventListener('click', function () {
        // Remove active class from all colors
        colorItems.forEach(color => color.classList.remove('active'));

        // Add active class to clicked color
        this.classList.add('active');

        // Update main image if data-img exists
        const imgSrc = this.getAttribute('data-img');
        if (imgSrc) {
          updateMainImage(imgSrc);
        }
      });
    });
  }

  function initQuantityControls() {
    // Remove any existing event listeners first to prevent conflicts
    const quantityControl = modal.querySelector('.product-modal__quantity-control');
    if (!quantityControl) return;

    // Get fresh references each time
    let quantityInput = quantityControl.querySelector('.product-modal__quantity-input');
    const btnUp = quantityControl.querySelector('.product-modal__btn.quantity__btn-up');
    const btnDown = quantityControl.querySelector('.product-modal__btn.quantity__btn-down');

    // Clone input first to remove existing listeners
    if (quantityInput) {
      const newQuantityInput = quantityInput.cloneNode(true);
      quantityInput.parentNode.replaceChild(newQuantityInput, quantityInput);
      quantityInput = newQuantityInput; // Update reference

      quantityInput.addEventListener('input', function () {
        let value = parseInt(this.value) || 1;
        if (value < 1) value = 1;
        this.value = value;
      });
    }

    // Remove existing event listeners by cloning elements
    if (btnUp && quantityInput) {
      const newBtnUp = btnUp.cloneNode(true);
      btnUp.parentNode.replaceChild(newBtnUp, btnUp);

      newBtnUp.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let currentValue = parseInt(quantityInput.value) || 1;
        quantityInput.value = currentValue + 1;
      });
    }

    if (btnDown && quantityInput) {
      const newBtnDown = btnDown.cloneNode(true);
      btnDown.parentNode.replaceChild(newBtnDown, btnDown);

      newBtnDown.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let currentValue = parseInt(quantityInput.value) || 1;
        if (currentValue > 1) {
          quantityInput.value = currentValue - 1;
        }

      });
    }
  }

  function updateColorsForSize(availableColors) {
    const colorContainer = modal.querySelector('.product-modal__options-right .product-colors');
    if (!colorContainer || !availableColors.length) return;

    colorContainer.innerHTML = '';
    availableColors.forEach((colorData, index) => {
      const colorElement = document.createElement('li');
      colorElement.className = 'product-color';
      if (index === 0) colorElement.classList.add('active'); // First color active by default

      colorElement.setAttribute('data-id', 'pro4');
      if (colorData.img) {
        colorElement.setAttribute('data-img', colorData.img);
      }

      const colorSpan = document.createElement('span');
      colorSpan.className = 'product-color__item';
      colorSpan.style.backgroundColor = colorData.color;

      colorElement.appendChild(colorSpan);
      colorContainer.appendChild(colorElement);
    });

    // Update main image to first color's image
    if (availableColors[0] && availableColors[0].img) {
      updateMainImage(availableColors[0].img);
    }

    // Reinitialize color selection for new elements
    initColorSelection();
  }

  function updateMainImage(imgSrc) {
    // Update the first swiper slide image
    const firstSlideImg = modal.querySelector('.swiper-slide img');
    if (firstSlideImg) {
      firstSlideImg.src = imgSrc;
    }

    // Update the first thumbnail
    const firstThumbnail = modal.querySelector('.product-thumbnails img');
    if (firstThumbnail) {
      firstThumbnail.src = imgSrc;
    }

    // Update the hidden direct image for cart compatibility
    const directImg = modal.querySelector('.product-modal__content .product__img[style*="display: none"]');
    if (directImg) {
      directImg.src = imgSrc;
    }
  }

  function initQuickViewSwiper() {
    quickViewSwiper = new Swiper(".swiper-quick-view", {
      slidesPerView: 1,
      spaceBetween: 8,
    });

    document.querySelectorAll(".product-thumbnails img").forEach((thumb, index) => {
      thumb.addEventListener("click", () => {
        quickViewSwiper.slideTo(index);
        document.querySelectorAll(".product-thumbnails img")
          .forEach((img) => img.classList.remove("active"));
        thumb.classList.add("active");
      });
    });
  }

  function formatModalInfo() {
    // Format links in product info with commas
    document.querySelectorAll(".product-modal__info p").forEach((p) => {
      const links = p.querySelectorAll("a");
      links.forEach((link, index) => {
        if (index < links.length - 1) {
          link.insertAdjacentText("afterend", ", ");
        }
      });
    });
  }

  function initFeatureSwiper() {
    const slideCount = document.querySelectorAll(".swiper-feature-offer .swiper-slide").length;
    const enableLoop = slideCount >= 3;
    const enableAutoplay = slideCount > 1;
    const emInPx = parseFloat(getComputedStyle(document.body).fontSize) * 1.5;

    new Swiper(".swiper-feature-offer", {
      autoplay: enableAutoplay ? {
        delay: 3000,
        disableOnInteraction: true,
      } : false,
      navigation: {
        nextEl: ".collection__next",
        prevEl: ".collection__prev",
      },
      speed: 1200,
      loop: enableLoop,
      spaceBetween: emInPx,
      breakpoints: {
        0: { slidesPerView: 1 },
        769: { slidesPerView: 3 },
      },
    });
  }

  return {
    init: function () {
      initModal();
      initQuickViewSwiper();
      initFeatureSwiper();
    }
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  quickViewModal.init();
});