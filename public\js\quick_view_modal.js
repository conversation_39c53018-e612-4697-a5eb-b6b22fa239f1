const quickViewModal = (function () {
  let modal, closeBtn, quickViewSwiper;

  function initModal() {
    modal = document.getElementById("product-modal");
    closeBtn = document.getElementById("modal-close");

    if (!modal || !closeBtn) return;

    document.querySelectorAll(".btn-quick-view").forEach((btn) => {
      btn.addEventListener("click", function (e) {
        e.preventDefault();
        const productElement = this.closest('article');
        loadProductData(productElement);
        openModal();
      });
    });

    closeBtn.addEventListener("click", closeModal);
    modal.addEventListener("click", (e) => {
      if (e.target.classList.contains("product-modal__overlay")) {
        closeModal();
      }
    });
  }

  function loadProductData(productElement) {
    if (!productElement || !modal) return;

    // Extract product data from the product element
    const productData = extractProductData(productElement);

    // Update modal content with product data
    updateModalContent(productData);
  }

  function extractProductData(productElement) {
    const data = {};

    // Get product images
    const mainImg = productElement.querySelector('.product__img');
    const hoverImg = productElement.querySelector('.product__img--hover');
    data.images = [];
    if (mainImg) data.images.push(mainImg.src);
    if (hoverImg) data.images.push(hoverImg.src);

    // Get product title and link
    const titleLink = productElement.querySelector('.product__title a');
    data.title = titleLink ? titleLink.textContent.trim() : '';
    data.link = titleLink ? titleLink.href : '#';

    // Get product prices
    const salePrice = productElement.querySelector('.sale-price');
    const regularPrice = productElement.querySelector('.product-prices span:not(.sale-price)');
    data.salePrice = salePrice ? salePrice.textContent.trim() : '';
    data.regularPrice = regularPrice ? regularPrice.textContent.trim() : '';

    // Get product rating
    const ratingStars = productElement.querySelectorAll('.mb-start-fill').length;
    const soldCount = productElement.querySelector('.product-rating__selled');
    data.rating = ratingStars;
    data.soldCount = soldCount ? soldCount.textContent.trim() : '';

    // Get product tags
    data.tags = [];
    const tagElements = productElement.querySelectorAll('.product-tag__item span');
    tagElements.forEach(tag => {
      data.tags.push({
        text: tag.textContent.trim(),
        type: tag.parentElement.classList.contains('product-tag__item--sale') ? 'sale' :
          tag.parentElement.classList.contains('product-tag__item--new') ? 'new' : 'hot'
      });
    });

    // Get product colors
    data.colors = [];
    const colorElements = productElement.querySelectorAll('.product-color');
    colorElements.forEach(colorEl => {
      if (!colorEl.classList.contains('product-color--more')) {
        const colorSpan = colorEl.querySelector('.product-color__item');
        const img = colorEl.getAttribute('data-img');
        if (colorSpan) {
          data.colors.push({
            color: colorSpan.style.backgroundColor,
            image: img,
            active: colorEl.classList.contains('active')
          });
        }
      }
    });

    // Get product sizes with color data
    data.sizes = [];
    const sizeElements = productElement.querySelectorAll('.product-size__item');
    sizeElements.forEach(sizeEl => {
      const sizeText = sizeEl.textContent.trim();
      const colorData = sizeEl.getAttribute('data-color');
      data.sizes.push({
        size: sizeText,
        colorData: colorData ? JSON.parse(colorData) : [],
        active: sizeEl.classList.contains('product-size--active')
      });
    });

    // Get product ID from quick view button
    const quickViewBtn = productElement.querySelector('.btn-quick-view[data-product-id]');
    data.productId = quickViewBtn ? quickViewBtn.getAttribute('data-product-id') : '1';

    // Get additional product data from data attributes
    data.description = productElement.getAttribute('data-description') || '';
    data.productCode = productElement.getAttribute('data-product-code') || '';
    data.categories = productElement.getAttribute('data-categories') || '';
    data.productTags = productElement.getAttribute('data-tags') || '';

    return data;
  }

  function updateModalContent(data) {
    // Update product images
    updateProductImages(data.images);

    // Update product title
    const titleElement = modal.querySelector('.product-modal__title a');
    if (titleElement) {
      titleElement.textContent = data.title;
      titleElement.href = data.link;
    }

    // Update product tags
    updateProductTags(data.tags);

    // Update product prices
    updateProductPrices(data.salePrice, data.regularPrice);

    // Update product rating
    updateProductRating(data.rating, data.soldCount);

    // Update product colors
    updateProductColors(data.colors);

    // Update product sizes
    updateProductSizes(data.sizes);

    // Update product ID in add to cart button
    const addToCartBtn = modal.querySelector('.btn_add_to_cart');
    if (addToCartBtn) {
      addToCartBtn.setAttribute('data-product-id', data.productId);
    }

    // Update product description
    updateProductDescription(data.description);

    // Update product info (code, categories, tags)
    updateProductInfo(data.productCode, data.categories, data.productTags);

    // Format modal info after updating content
    setTimeout(() => formatModalInfo(), 100);
  }

  function openModal() {
    modal.classList.remove("product-modal--hidden");
    document.body.classList.add("modal-open");
  }

  function closeModal() {
    modal.classList.add("product-modal--hidden");
    document.body.classList.remove("modal-open");
  }

  function updateProductImages(images) {
    if (!images || images.length === 0) return;

    // Update thumbnails
    const thumbnailContainer = modal.querySelector('.product-thumbnails');
    if (thumbnailContainer) {
      thumbnailContainer.innerHTML = '';
      images.forEach((imgSrc, index) => {
        const img = document.createElement('img');
        img.src = imgSrc;
        img.setAttribute('data-slide', index);
        if (index === 0) img.classList.add('active');
        thumbnailContainer.appendChild(img);
      });
    }

    // Update swiper slides
    const swiperWrapper = modal.querySelector('.swiper-wrapper');
    if (swiperWrapper) {
      swiperWrapper.innerHTML = '';
      images.forEach((imgSrc) => {
        const slide = document.createElement('div');
        slide.className = 'swiper-slide';
        slide.style.width = '100%';
        slide.style.marginRight = '1.2em';
        slide.innerHTML = `
          <div class="product__box product__style wow fadeInUp will-animate">
            <div class="product__thumb-img">
              <img src="${imgSrc}" alt="Product Image" />
            </div>
          </div>
        `;
        swiperWrapper.appendChild(slide);
      });
    }

    // Reinitialize swiper after updating slides
    if (quickViewSwiper) {
      quickViewSwiper.destroy(true, true);
    }
    initQuickViewSwiper();
  }

  function updateProductTags(tags) {
    const tagContainer = modal.querySelector('.product-modal__content-box');
    if (!tagContainer) return;

    tagContainer.innerHTML = '';
    tags.forEach(tag => {
      const tagElement = document.createElement('span');
      tagElement.className = 'product-modal__label';

      if (tag.type === 'sale') {
        tagElement.classList.add('product-modal__sale-percent');
      } else if (tag.type === 'hot') {
        tagElement.classList.add('product-modal__sale-hot');
      }

      tagElement.textContent = tag.text;
      tagContainer.appendChild(tagElement);
    });
  }

  function updateProductPrices(salePrice, regularPrice) {
    const priceContainer = modal.querySelector('.product-modal__price');
    if (!priceContainer) return;

    let priceHTML = '';
    if (salePrice) {
      priceHTML = salePrice;
      if (regularPrice && regularPrice !== salePrice) {
        priceHTML += ` <del class="product-modal__old-price">${regularPrice}</del>`;
      }
    } else if (regularPrice) {
      priceHTML = regularPrice;
    }

    priceContainer.innerHTML = priceHTML;
  }

  function updateProductRating(rating, soldCount) {
    // Update rating stars
    const ratingContainer = modal.querySelector('.mb-rating');
    if (ratingContainer) {
      ratingContainer.innerHTML = '';
      for (let i = 1; i <= 5; i++) {
        const star = document.createElement('svg');
        star.className = i <= rating ? 'mb-start-fill' : 'mb-start';
        star.innerHTML = `<use href="${i <= rating ? '#icon-star-active' : '#icon-star-fill'}"></use>`;
        ratingContainer.appendChild(star);
      }
    }

    // Update sold count
    const soldElement = modal.querySelector('.product-rating__selled');
    if (soldElement && soldCount) {
      soldElement.textContent = soldCount;
    }
  }

  function updateProductColors(colors) {
    const colorContainer = modal.querySelector('.product-modal__options-right .product-colors');
    if (!colorContainer || !colors.length) return;

    colorContainer.innerHTML = '';
    colors.forEach((colorData) => {
      const colorElement = document.createElement('li');
      colorElement.className = 'product-color';
      if (colorData.active) colorElement.classList.add('active');

      colorElement.setAttribute('data-id', 'pro4');
      if (colorData.image) {
        colorElement.setAttribute('data-img', colorData.image);
      }

      const colorSpan = document.createElement('span');
      colorSpan.className = 'product-color__item';
      colorSpan.style.backgroundColor = colorData.color;

      colorElement.appendChild(colorSpan);
      colorContainer.appendChild(colorElement);
    });
  }

  function updateProductSizes(sizes) {
    const sizeContainer = modal.querySelector('.product-modal__options-left .product-size');
    if (!sizeContainer || !sizes.length) return;

    sizeContainer.innerHTML = '';
    sizes.forEach(sizeData => {
      const sizeElement = document.createElement('li');
      sizeElement.className = 'product-size__item maybi-hover';
      if (sizeData.active) sizeElement.classList.add('product-size--active');

      if (sizeData.colorData && sizeData.colorData.length > 0) {
        sizeElement.setAttribute('data-color', JSON.stringify(sizeData.colorData));
      }

      sizeElement.textContent = sizeData.size;
      sizeContainer.appendChild(sizeElement);
    });
  }

  function updateProductDescription(description) {
    const descElement = modal.querySelector('.product-modal__desc');
    if (descElement && description) {
      descElement.textContent = description;
    }
  }

  function updateProductInfo(productCode, categories, tags) {
    const infoContainer = modal.querySelector('.product-modal__info');
    if (!infoContainer) return;

    let infoHTML = '';

    // Product code
    if (productCode) {
      infoHTML += `<p><strong>Mã sản phẩm:</strong> ${productCode}</p>`;
    }

    // Categories
    if (categories) {
      const categoryList = categories.split(',').map(cat => cat.trim());
      const categoryLinks = categoryList.map(cat => `<a href="#" title="${cat}">${cat}</a>`).join('');
      infoHTML += `<p><strong>Danh mục:</strong> ${categoryLinks}</p>`;
    }

    // Tags
    if (tags) {
      const tagList = tags.split(',').map(tag => tag.trim());
      const tagLinks = tagList.map(tag => `<a href="#" title="${tag}">${tag}</a>`).join('');
      infoHTML += `<p><strong>Nhãn:</strong> ${tagLinks}</p>`;
    }

    infoContainer.innerHTML = infoHTML;
  }

  function initQuickViewSwiper() {
    quickViewSwiper = new Swiper(".swiper-quick-view", {
      slidesPerView: 1,
      spaceBetween: 8,
    });

    document.querySelectorAll(".product-thumbnails img").forEach((thumb, index) => {
      thumb.addEventListener("click", () => {
        quickViewSwiper.slideTo(index);
        document.querySelectorAll(".product-thumbnails img")
          .forEach((img) => img.classList.remove("active"));
        thumb.classList.add("active");
      });
    });
  }

  function formatModalInfo() {
    // Format links in product info with commas
    document.querySelectorAll(".product-modal__info p").forEach((p) => {
      const links = p.querySelectorAll("a");
      links.forEach((link, index) => {
        if (index < links.length - 1) {
          link.insertAdjacentText("afterend", ", ");
        }
      });
    });
  }

  function initFeatureSwiper() {
    const slideCount = document.querySelectorAll(".swiper-feature-offer .swiper-slide").length;
    const enableLoop = slideCount >= 3;
    const enableAutoplay = slideCount > 1;
    const emInPx = parseFloat(getComputedStyle(document.body).fontSize) * 1.5;

    new Swiper(".swiper-feature-offer", {
      autoplay: enableAutoplay ? {
        delay: 3000,
        disableOnInteraction: true,
      } : false,
      navigation: {
        nextEl: ".collection__next",
        prevEl: ".collection__prev",
      },
      speed: 1200,
      loop: enableLoop,
      spaceBetween: emInPx,
      breakpoints: {
        0: { slidesPerView: 1 },
        769: { slidesPerView: 3 },
      },
    });
  }

  return {
    init: function () {
      initModal();
      initQuickViewSwiper();
      initFeatureSwiper();
    }
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  quickViewModal.init();
});