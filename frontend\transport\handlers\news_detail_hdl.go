package handlers

import (
	"goweb/views/news"

	"github.com/gofiber/fiber/v2"
	"github.com/networld-solution/gos/templates"
)

type newsDetailHdl struct {
}

func NewNewsDetailHdl() *newsDetailHdl {
	return &newsDetailHdl{}
}

/**
 * News detail hdl
 */
func (h *newsDetailHdl) NewsDetailHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Có thể lấy slug từ URL params nếu cần
		// slug := c.Params("slug")
		
		return templates.Render(c, news.NewsDetail())
	}
}
