function OffcanvasCartProductItemCmp(item) {
    const sizeHTML = item.sizes.map(size => {
        const dataColorAttr = size.dataColor ? `data-color='${size.dataColor}'` : '';
        return `<li class="product-size__item maybi-hover ${size.isActive ? "product-size--active" : ""}"
        ${dataColorAttr}>${size.text}</li>`;
    }).join("");

    const colorHTML = item.colors.map(color =>
        `<li class="product-color ${color.isActive ? "active" : ""}" data-img="${color.dataImg}">
            <span class="product-color__item" style="background-color: ${color.backgroundColor};"></span>
        </li>
        `
    ).join("");

    // Check if this is a gift item
    const isGift = item.isGift || false;
    const giftClass = isGift ? ' gift-item' : '';
    const giftBadge = isGift ? '<div class="gift-badge">Tặng</div>' : '';

    return `
    <div class="cart__item product${giftClass}" data-id="${item.product_id}">
        <div class="cart__item__img">
        <img class="product__img" src="${item.image}" alt="${item.name}" />
        ${giftBadge}
        </div>
        <div class="cart__item__content product__info">
        <strong class="cart__item__title">
            <a href="#" title="${item.name}">${item.name}</a>
        </strong>
        <div class="cart__item__info">
            <div class="quantity-cart">
            <button class="quantity-cart__btn quantity-cart__btn-down" type="button">
                <svg class="product-icon__down"><use href="#icon-down"></use></svg>
            </button>
            <input class="quantity-cart__input" type="text" aria-label="Số lượng sản phẩm" value="${item.quantity}" />
            <button class="quantity-cart__btn quantity-cart__btn-up" type="button">
                <svg class="product-icon__up"><use href="#icon-up"></use></svg>
            </button>
            </div>
            <div class="cart__item__price">
            <span class="cart__item__price--sale">${item.price || "--"}</span>
            </div>
        </div>
        <div class="cart__item__variants">
            <span class="text-muted">Size:</span>
            <ul class="product-size">${sizeHTML}</ul>
        </div>
        <div class="cart__item__variants">
            <span class="text-muted">Màu:</span>
            <ul class="product-colors">${colorHTML}</ul>
        </div>
        </div>
        <div class="cart__item__delete">
        <svg class="cart-icon__delete"><use href="#icon-close"></use></svg>
        </div>
    </div>
    `;
}

function CartProductItemCmp(item, subtotal) {
    const sizeHTML = item.sizes.map(size => {
        const dataColorAttr = size.dataColor ? `data-color='${size.dataColor}'` : '';
        return `<li class="product-size__item maybi-hover ${size.isActive ? "product-size--active" : ""}"
          ${dataColorAttr}>${size.text}</li>`;
    }).join("");

    const colorHTML = item.colors.map(color => `
      <li class="product-color ${color.isActive ? "active" : ""}" data-img="${color.dataImg}">
        <span class="product-color__item" style="background-color: ${color.backgroundColor};"></span>
      </li>
    `).join("");

    // Check if this is a gift item
    const isGift = item.isGift || false;
    const giftClass = isGift ? ' gift-item' : '';
    const giftBadge = isGift ? '<div class="gift-badge">Tặng</div>' : '';

    return `
    <div class="cart__item product__item product${giftClass}" data-id="${item.product_id}">
        <div class="cart__item__img">
            <img class="product__img" src="${item.image}" alt="${item.name}" />
            ${giftBadge}
        </div>
        <div class="cart__item__details">
            <div class="col__information__content product__info">
                <strong class="cart__item__title">
                    <a href="#" title="${item.name}">${item.name}</a>
                </strong>
                <div class="cart__item__variants">
                    <span class="text-muted">Size:</span>
                    <ul class="product-size">${sizeHTML}</ul>
                </div>
                <div class="cart__item__variants">
                    <span class="text-muted">Màu:</span>
                    <ul class="product-colors">${colorHTML}</ul>
                </div>
            </div>
            <div class="item-detail_price-quantity">
                <div class="item-detail_price-wrapper text-center">
                    <span class="item-detail_label text-muted">Giá:</span>
                    <strong class="col__price text-muted cart__item__price--sale text-center" data-label="Price">${item.price}</strong>
                </div>
                <div class="item-detail_quantity-wrapper">
                    <span class="item-detail_label text-muted">Số lượng:</span>
                    <div class="quantity-cart">
                        <button class="quantity-cart__btn quantity-cart__btn-down" type="button" aria-label="Giảm số lượng">
                            <svg class="product-icon__down"><use href="#icon-down"></use></svg>
                        </button>
                        <input class="quantity-cart__input" type="text" aria-label="Số lượng sản phẩm" value="${item.quantity}" />
                        <button class="quantity-cart__btn quantity-cart__btn-up" type="button" aria-label="Tăng số lượng">
                            <svg class="product-icon__up"><use href="#icon-up"></use></svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="cart__item__summary">
            <div class="cart__item__total product-total text-center" data-label="Subtotal">${subtotal}</div>
            <div class="cart__item__action cart__item__delete">
                <svg class="cart__item__icon"><use href="#icon-close"></use></svg>
            </div>
        </div>
    </div>
    `;
}

// Functions are available globally - no export needed
