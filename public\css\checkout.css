.checkout__wrapper {
    font-size: var(--10px);
    position: relative;
}

.checkout__box {
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

.checkout__left {
    width: 65%;
    padding-right: 2em;
    padding-top: 3em;
}

.checkout__right {
    display: block;
    width: 35%;
    padding: 3em 2em;
}

/* ------- Breadcrumb ------- */
.checkout__breadcrumb {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: .5em;
    margin-top: 1em;
}

.checkout__breadcrumb-item:not(:first-child)::before {
    content: ">";
    margin: 0 .5em;
    color: var(--black);
    font-weight: 600;
    font-size: 1.2em;
}

.checkout__breadcrumb-item>a,
.checkout__breadcrumb-item>span {
    font-size: 1.2em;
    font-weight: 500;
}

/* ------------- Content Form ------------- */
.checkout__main-header {
    padding-bottom: 1em;
}

.form__header {
    margin-bottom: 1.5em;
}

.form__header>h2,
.order__shipping-method>h2,
.checkout__payment-method__box>h2 {
    font-weight: 600;
    font-size: 1.55em;
}

.form__auth-redirect {
    font-size: var(--10px);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: .5em;
    margin-bottom: .75em;
}

.form__auth-redirect>p {
    margin-bottom: 0;
    font-size: 1.2em;
    color: rgba(var(--rgb-black), .5);
}

.form__auth-redirect>a {
    font-size: 1.2em;
    font-weight: 500;
    color: var(--text-yellow);
}

/* --- Customer input field --- */
.customer__inuput-field,
.form__note,
.summary-discount__content {
    position: relative;
    width: 100%;
    padding: 0.45em 0;
}

.customer__inuput-field>input {
    font-size: var(--10px);
    width: 100%;
    height: 3.5em;
    background-color: transparent;
    padding: 1.2em 1em 0.4em;
    box-sizing: border-box;
    border: .08em solid rgba(var(--rgb-black), .3);
    border-radius: .5em;
    transition: border-color 0.3s ease
}

.customer__inuput-field>input:focus {
    border-color: var(--primary);
    outline: none;
}

.customer__inuput-field>label.shipping__label {
    position: absolute;
    left: .5em;
    top: .5em;
    color: rgba(var(--rgb-black), .3);
    font-size: 1.2em;
    pointer-events: none;
    transition: 0.2s ease all;
}

.customer__inuput-field>input:focus+label,
.customer__inuput-field>input:not(:placeholder-shown)+label {
    top: 0;
    left: .2em;
    font-size: 1em;
    color: var(--primary);
}

.customer__email.customer__inuput-field>input:focus+label,
.customer__email.customer__inuput-field>input:not(:placeholder-shown)+label {
    left: .7em;
}

/* ----- end input field ----- */

.customer__email-phone__box,
.customer__phone-address__box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.45em 0;
}

.customer__email,
.recipient_street {
    width: 65%;
    padding-left: .5em;
}

.customer__phone,
.recipient__phone {
    width: 35%;
    padding-right: .5em;
}

.customer__street {
    padding: .45em 0;
}

.form__order-toggle {
    display: flex;
    align-items: center;
    gap: 1em;
    margin: 1em 0;
}

.form__order-toggle>input {
    font-size: var(--10px);
    width: 1.3em;
    height: 1.3em;
    cursor: pointer;
}

.form__order-toggle>label {
    font-size: 1.3em;
    font-weight: 500;
    color: var(--text-yellow);
    cursor: pointer;
}

/* -------- Address -------- */
.customer__shipping__group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1em;
    margin-top: .45em;
}

.shipping__province,
.shipping__district,
.shipping__ward {
    width: 33%;
    position: relative;
    padding: 0.45em 0;
}

.shipping__label {
    font-size: 1em;
    font-weight: normal;
    position: absolute;
    top: 0;
    width: 100%;
    padding: 0 0.93333em;
    z-index: 1;
    user-select: none;
    pointer-events: none;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    box-sizing: border-box;
    color: var(--light-grey);
    transition: all 0.2s ease-out;
    margin-top: 0.8em;
    display: block;
}

.shipping__province>select,
.shipping__district>select,
.shipping__ward>select {
    font-size: var(--10px);
    font-weight: 500;
    border: .08em solid rgba(var(--rgb-black), .3);
    transition: all 0.2s ease-out;
    background-color: transparent;
    color: var(--dark-grey);
    border-radius: .3em;
    display: block;
    box-sizing: border-box;
    width: 100%;
    padding: 1.8em 2.8em 0.5em 1em;
    word-break: normal;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.shipping__province::before,
.shipping__district::before,
.shipping__ward::before {
    content: "";
    position: absolute;
    width: .1em;
    height: 50%;
    top: 1.3em;
    bottom: 0;
    right: 3em;
    background-color: rgba(var(--rgb-black), .2);
}

.shipping__province::after,
.shipping__district::after,
.shipping__ward::after {
    content: "";
    position: absolute;
    top: 33%;
    right: .4em;
    font-size: 1.5em;
    transform: rotate(90deg);
    pointer-events: none;
    color: rgba(var(--rgb-black), 0.5);
    width: 1.25em;
    height: 1.25em;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z'%3E%3C/path%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.7;
    vertical-align: middle;
}

/* ------ Note in column left ------ */

.note_label {
    font-size: 1em;
    font-weight: normal;
    position: absolute;
    top: 0;
    width: 100%;
    padding: 0 0.93333em;
    user-select: none;
    pointer-events: none;
    overflow: hidden;
    text-overflow: ellipsis;
    box-sizing: border-box;
    color: var(--light-grey);
    transition: all 0.2s ease-out;
    margin-top: 0.8em;
    display: block;
}

.form__note {
    position: relative;
    width: 100%;
    padding: 0.45em 0;
}

.form__note>textarea {
    font-size: 1em;
    width: 100%;
    background-color: transparent;
    padding: 1.5em 1em 0.4em;
    box-sizing: border-box;
    border: .08em solid rgba(var(--rgb-black), .3);
    border-radius: .5em;
    transition: border-color 0.3s ease;
    max-height: 10em;
    min-height: 3.5em;
    resize: vertical;
}

.form__note>textarea:focus {
    border-color: var(--primary);
    outline: none;
}

.form__note>label.note_label {
    position: absolute;
    left: .5em;
    top: .5em;
    color: rgba(var(--rgb-black), .3);
    font-size: 1.2em;
    pointer-events: none;
    transition: 0.2s ease all;
}

.form__note>textarea:focus+label,
.form__note>textarea:not(:placeholder-shown)+label {
    top: 0;
    left: .2em;
    font-size: 1em;
    color: var(--primary);
}

/* -------- Order right -------- */

.order-detail__item {
    width: 100%;
    font-size: 1.2em;
    display: flex;
    align-items: center;
    border-bottom: .1em solid rgba(var(--rgb-black), 0.2);
    padding-bottom: 1.3em;
    margin-bottom: 1.3em;
    gap: 2em;
}

.order-detail__item-thumb {
    position: relative;
    width: 5.5em;
    height: 5.5em;
}

.order-detail__item-thumb>img {
    width: 100%;
    height: 100%;
    border-radius: 1em;
    object-fit: cover;
    object-position: center;
}

.order-detail__item-quantity {
    position: absolute;
    top: -.7em;
    right: -.5em;
    z-index: 2;
    color: var(--white);
    background-color: var(--light-grey);
    border-radius: 2em;
    width: 1.5em;
    height: 1.5em;
    display: flex;
    align-items: center;
    justify-content: center;
}

.order-detail__item-info {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1em;
}

.order__item-info__box {
    flex: 2;
}

.order-detail__item-info>span {
    font-size: 1.38em;
    font-weight: 500;
}

.order__item-info__box>h6 {
    font-size: 1.38em;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: justify;
}

.order__item-info__box>span {
    font-size: 1.2em;
    font-weight: 400;
    color: rgba(var(--rgb-black), 0.5);
}

.order__item-info__box>span:not(:first-of-type)::before {
    content: "/";
    margin: 0 .3em;
    color: rgba(var(--rgb-black), 0.3);
    font-weight: 600;
    font-size: 1.2em;
}

.order__item-price {
    font-size: var(--10px);
    line-height: 1;
}

.order__item-price>span {
    font-size: 1.38em;
    font-weight: 500;
}

.order__item-price>span+span {
    font-size: 1.2em;
    color: rgba(var(--rgb-black), 0.7);
    font-weight: 500;
    margin-left: 0.5em;
    text-decoration: line-through;
}

/* -------- Order summary -------- */
.order__summary-subtotal {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.3em;
}

.order__summary-subtotal>span,
.order__summary-shipping>span,
.order__summary-discount-price>span {
    font-size: 1.38em;
    font-weight: 500;
}

.order__summary-shipping {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding-top: 1.3em;
}

.order__summary-total {
    padding: 2em 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.order__summary-total>span {
    font-size: 1.38em;
    font-weight: 500;
}

.order__summary-total>span+span {
    font-size: 2.05em;
    font-weight: 600;
    color: var(--primary);
}

.order__summary-discount-price {
    display: flex;
    justify-content: space-between;
    padding-top: 1.3em;
    border-bottom: .1em solid rgba(var(--rgb-black), 0.2);
    padding: 1.3em 0;
}

.payment-method__checkbox,
.shipping-method__wrapper,
.shipping-method__checkbox {
    display: flex;
    align-items: center;
    gap: 1.5em;
}

.shipping-method__wrapper {
    justify-content: space-between;
    border: .1em solid rgba(var(--rgb-black), 0.2);
    border-radius: 1em;
    padding: 1.5em;
}

.shipping-method__wrapper:last-child {
    margin-bottom: 1.3em;
}

.payment-method__checkbox>input,
.shipping-method__checkbox>input {
    font-size: var(--10px);
    width: 1.4em;
    height: 1.4em;
    font-weight: 500;
    cursor: pointer;
}

.payment-method__checkbox>label,
.shipping-method__checkbox>label,
.shipping-method__wrapper>span {
    font-size: 1.38em;
    font-weight: 500;
    cursor: pointer;
}

.payment-method__checkbox>svg {
    width: 3em;
    height: 3em;
}

.checkout__payment-method__box {
    margin-top: 1.5em;
}

.checkout__payment-methods {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    border: .1em solid rgba(var(--rgb-black), 0.2);
    border-radius: 1em;
    margin: 1.5em 0;
}

.checkout__payment-methods>.payment-method__item {
    padding: 1.3em;
}

.checkout__payment-methods>.payment-method__item:not(:last-child) {
    border-bottom: .1em solid rgba(var(--rgb-black), 0.2);
}

.payment-method__body>p {
    font-size: 1.2em;
    font-weight: 400;
    margin-top: 1em;
    color: rgba(var(--rgb-black), 0.6);
}

.payment-method__what-paypal {
    color: rgba(var(--rgb-black), 0.5);
}

.checkout__privacy-note>p {
    font-weight: 400;
    font-size: 1.2em;
    margin: 1.38em 0;
    color: rgba(var(--rgb-black), 0.5);
}

.checkout__privacy-note>p>a {
    color: var(--primary);
    text-decoration: underline;
}

.checkout__form-group {
    margin-bottom: 1.4em;
}

.form__checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 1.5em;
}

.form__checkbox-wrapper>.form__checkbox-label {
    font-size: 1.2em;
    font-weight: 400;
    color: rgba(var(--rgb-black), 0.5);
    cursor: pointer;
}

.form__checkbox-wrapper>input {
    font-size: var(--10px);
    width: 1.3em;
    height: 1.3em;
}

.order__shipping-method {
    border-bottom: .1em solid rgba(var(--rgb-black), 0.2);
    padding: 1.3em 0;
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.summary-discount__content>input {
    font-size: var(--10px);
    width: 100%;
    height: 3.5em;
    background-color: transparent;
    padding: 1.2em 1em 0.4em;
    box-sizing: border-box;
    border: .08em solid rgba(var(--rgb-black), .3);
    border-radius: .5em;
    transition: border-color 0.3s ease
}

.summary-discount__content>input:focus {
    border-color: var(--primary);
    outline: none;
}

.summary-discount__content>label.discount-label {
    position: absolute;
    left: 1em;
    top: 1.4em;
    color: rgba(var(--rgb-black), .3);
    font-size: 1.2em;
    pointer-events: none;
    transition: 0.2s ease all;
}

.summary-discount__content>input:focus+label,
.summary-discount__content>input:not(:placeholder-shown)+label {
    top: .7em;
    left: 1em;
    font-size: 1em;
    color: var(--primary);
}

.summary-discount__box-content {
    display: flex;
    align-items: center;
    margin: 1.3em 0;
    gap: 1.5em;
}

.summary-discount__box-content>button {
    white-space: nowrap;
    height: 100%;
    padding: 1em 1.8em;
    border: none;
    border-radius: 0.3em;
    font-size: 1em;
    font-weight: 500;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.summary-discount__box-content>button>span {
    font-size: 1.5em;
    font-weight: 500;
}

.summary-discount__box-content>button:disabled {
    height: 100%;
    background-color: var(--light-grey);
    color: var(--white);
    cursor: not-allowed;
}

.summary-discount__box-content>button:not(:disabled) {
    background-color: var(--black);
    color: var(--white);
    cursor: pointer;
}

.discount__choose-coupons {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
}

.discount__choose-coupons__content {
    display: flex;
    align-items: center;
    gap: 1em;
    cursor: pointer;
}

.discount__choose-coupons__content>svg {
    width: 2.5em;
    height: 2.5em;
    fill: var(--text-yellow);

}

.discount__choose-coupons__content>span {
    font-size: 1.2em;
    font-weight: 500;
    color: var(--text-yellow);
}

.cart-vouchers__chip>span {
    font-size: 1.2em;
}

/* -------- Btn submit -------- */

.btn {
    padding: 1.1em 1.75em;
    cursor: pointer;
    border-radius: 0.7em;
    border: .1em solid var(--black);
}

.btn--primary>span,
.btn--outline>span {
    font-size: 1.4em;
}

.btn--primary>span,
.btn--outline>span {
    text-transform: uppercase;
}

.btn--primary {
    position: relative;
    overflow: hidden;
    background: var(--black);
    display: flex;
    color: var(--white);
    margin-right: 1em;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.btn--primary:hover {
    color: var(--white);
}

.btn--primary::after,
.btn--outline::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.8;
    visibility: hidden;
    height: 100%;
    width: 100%;
    transform: translateX(-105%);
    border-right: 0.2em solid var(--white);
    background-color: rgba(255, 255, 255, 0.5);
    transition: all 0.5s ease;
    pointer-events: none;
}

.btn--primary:hover::after,
.btn--outline:hover::after {
    transform: translate(0);
    opacity: 0;
    visibility: visible;
}

.btn--outline {
    position: relative;
    overflow: hidden;
    background: var(--white);
    color: var(--black);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1em;
    white-space: nowrap;
    width: 100%;
}



.btn--outline:hover {
    background-color: var(--black);
    color: var(--white);
}

/* Gift Product Size Button */
.gift-product__size-btn {
    width: fit-content;
    height: fit-content;
    font-size: 0.65em;
}

/* Gift Size List - consistent with product detail */
.gift-size-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.6em;
    margin: 0;
    list-style: none;
}

.gift-size-list .product-size__item {
    font-size: 1.3em;
    width: 3em;
    height: 3em;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    background-color: rgba(var(--rgb-white), 0.3);
    transition: all 0.3s ease;
}

.gift-size-list .product-size__item.product-size--active {
    background-color: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.gift-size-list .product-size__item:hover {
    background-color: var(--black);
    color: var(--white);
    border-color: var(--black);
}

/* Button shake animation for validation feedback */
@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-5px);
    }

    20%,
    40%,
    60%,
    80% {
        transform: translateX(5px);
    }
}

/* Gift Product Size Button */
.gift-product__size-btn {
    width: fit-content;
    font-size: 0.65em;
}

/* Gift Size List - consistent with product detail */
.gift-size-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.6em;
    margin: 0;
    list-style: none;
}

.gift-size-list .product-size__item {
    font-size: 1.3em;
    width: 3em;
    height: 3em;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    background-color: rgba(var(--rgb-white), 0.3);
    transition: all 0.3s ease;
}

.gift-size-list .product-size__item.product-size--active {
    background-color: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.gift-size-list .product-size__item:hover {
    background-color: var(--black);
    color: var(--white);
    border-color: var(--black);
}

/* ------ Offcanvas vouchers ------ */
.voucher__list {
    display: flex;
    flex-direction: column;
    gap: 2em;
}

.voucher-item-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 1em;
}

.voucher-item-wrapper__radio>input {
    font-size: var(--10px);
    width: 2em;
    height: 2em;
    border: 0.1em solid var(--black);
}

.voucher-item {
    width: 100%;
}

.voucher__list>label:last-child {
    opacity: 0.5;
    pointer-events: none;
    position: relative;
}

.voucher__list>label:last-child::after {
    content: "Voucher không khả dụng";
    position: absolute;
    top: 25%;
    left: 75%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.85);
    color: var(--primary);
    font-weight: bold;
    padding: 0.5em 1em;
    border-radius: 0.3em;
    font-size: 0.9em;
    z-index: 1;
}

/* -------- Content second -------- */
.checkout__summary__mobile,
.checkout__btn__mobile {
    display: none;
}

/* -------- Responsive -------- */
@media (max-width: 768px) {
    .checkout__box {
        flex-direction: column;
        padding: 0 1.5em;
    }

    .checkout__left {
        width: 100%;
        padding-right: 0;
    }

    .checkout__right {
        width: 100%;
        padding: 2em 0;
        display: none;
    }

    .checkout__summary__mobile,
    .checkout__btn__mobile {
        display: block;
        margin-bottom: 2em;
    }

    .checkout__summary__mobile {
        margin-top: 2em;
    }

    .checkout__main-content .checkout__payment-method__box {
        display: none;
    }

    .order__item-info__box>h6,
    .order__item-info__box>span {
        max-width: 14em;
        font-size: 1.4em;
    }

    .customer__email-phone__box,
    .customer__phone-address__box,
    .customer__shipping__group {
        flex-direction: column;
    }

    .customer__phone,
    .recipient__phone,
    .customer__email,
    .recipient_street,
    .shipping__province,
    .shipping__district,
    .shipping__ward {
        width: 100%;
    }

    .customer__email,
    .recipient_street {
        padding-left: 0;
    }

    .customer__email {
        padding-top: .8em;
    }

    .sheet__panel {
        width: 90%;
    }

    .order-detail__item,
    .order__item-price {
        font-size: 2vw;
    }

    .summary-discount__box-content>button {
        padding: 1.2em 1.8em;
    }

    .customer__inuput-field>label.shipping__label,
    .form__note>label.note_label {
        left: .3em;
        top: .8em;
    }

    .summary-discount__content>label.discount-label {
        top: 1.55em;
    }

    .form__header>h2,
    .order__shipping-method>h2,
    .checkout__payment-method__box>h2 {
        font-size: 2.1em;
    }

    .checkout__breadcrumb-item>a,
    .checkout__breadcrumb-item>span {
        font-size: 1.4em;
    }

    .payment-method__checkbox>label,
    .shipping-method__checkbox>label,
    .shipping-method__wrapper>span {
        font-size: 1.7em;
        font-weight: 400;
    }

    .payment-method__body>p {
        font-size: 1.4em;
    }

    .summary-discount__content>input,
    .customer__inuput-field>input {
        height: 4.5em;
    }

    .discount__choose-coupons__content>span {
        font-size: 1.4em;
    }

    .shipping__province>select,
    .shipping__district>select,
    .shipping__ward>select {
        padding: 2.15em 2.8em 1em .8em;
    }

    .customer__phone,
    .recipient__phone {
        padding-right: 0;
    }

    .customer__shipping__group {
        margin-top: 0;
        padding: .45em 0;
        gap: .45em;
    }

    .checkout_btn {
        padding: 2.5em 1.5em;
        margin-right: 0;
    }

    .checkout_btn>span {
        font-size: 2.2em;
    }
}

@media (max-width: 575px) {
    .checkout__form {
        font-size: 1.2em;
    }
    .summary-discount__content>input, .customer__inuput-field>input {
        height: 4.5em;
        font-size: 1em;
    }
    .customer__shipping__group {
        font-size: 1.2em;
    }
    .shipping__province>select, .shipping__district>select, .shipping__ward>select {
        font-size: 1em;
    }
}
/* Gift Product Component */
.gift-product {
    border-radius: 1em;
    padding: 3em 1em 1em;
    margin: 1em 0;
    position: relative;
    overflow: hidden;
    border: 0.1em solid var(--primary);
    font-size: 1.2em;
}

.gift-product__badge {
    display: flex;
    align-items: center;
    gap: 0.5em;
    background: linear-gradient(135deg,
            var(--primary) 0%,
            var(--primary-dark) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5em 1em;
    font-size: 0.875em;
    font-weight: 600;
    color: white;
    margin-bottom: 1em;
    width: fit-content;
    position: absolute;
    top: 0;
    left: 0;
    border-bottom-right-radius: 1.75em;
}

.gift-product__icon {
    width: 1.5em;
    height: 1.5em;
    color: white;
}

.gift-product__content {
    display: flex;
    gap: 2em;
    align-items: center;
}

.gift-product__image {
    height: 10em;
    aspect-ratio: 1;
    border-radius: 0.75em;
    overflow: hidden;
    flex-shrink: 0;
}

.gift-product__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.5em;
}

.gift-product__info {
    flex: 1;
    color: var(--black);
    display: flex;
    flex-direction: column;
    gap: 0.75em;
}

.gift-product__details {
    flex: 1;
}

.gift-product__actions {
    display: flex;
    justify-content: flex-start;
}

.gift-product__title {
    font-size: 1.5em;
    font-weight: 600;
    margin: 0 0 0.25em 0;
    line-height: 1.3;
}

.gift-product__desc {
    font-size: 0.85em;
    margin: 0 0 0.5em 0;
    opacity: 0.9;
    line-height: 1.4;
}

.gift-product__price {
    display: flex;
    align-items: flex-end;
    gap: 0.5em;
}

.gift-product__price-current {
    font-size: 1.25em;
    line-height: 1;
    font-weight: 700;
    color: var(--primary);
}

.gift-product__price-original {
    font-size: 0.775em;
    text-decoration: line-through;
    opacity: 0.7;
    color: var(--black);
}

.gift-product__size-btn {
    width: fit-content;
    font-size: 0.65em;
}

/* Gift Size Modal */
.gift-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1055;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.gift-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.gift-modal {
    background: white;
    border-radius: 1em;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-width: 50em;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.gift-modal-overlay.active .gift-modal {
    transform: scale(1);
}

.gift-modal__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1em 1.5em;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.gift-modal__title {
    margin: 0;
    font-size: 1.25em;
    font-weight: 600;
    color: var(--black);
}

.gift-modal__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25em;
    color: rgba(0, 0, 0, 0.5);
    transition: color 0.3s ease;
}

.gift-modal__close:hover {
    color: var(--black);
}

.gift-modal__body {
    padding: 1.5em;
}

.gift-modal__footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1em;
    padding: 1em 1.5em;
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.gift-modal__footer .btn {
    max-width: 15em;
    font-size: 1em;
}

.gift-modal__footer .btn--outline {
    margin-right: 0;
}

.gift-modal__footer .btn--primary {
    margin-right: 0;
}

.gift-size-selector {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
}

.gift-size-product {
    display: flex;
    align-items: center;
    gap: 1em;
    padding: 1em;
    background: #f8f9fa;
    border-radius: 0.5em;
}

.gift-size-product img {
    object-fit: cover;
    border-radius: 0.375em;
    height: 100%;
    height: 15vw;
    aspect-ratio: 3 / 4;
}

.gift-size-info {
    font-size: 1.5em;
}

.gift-size-info h6 {
    font-size: 1em;
    margin: 0 0 0.5em 0;
    font-weight: 600;
    color: var(--black);
}

.gift-size-info p {
    margin: 0;
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.9em;
}

.gift-size-options {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.gift-size-options h6 {
    margin: 0;
    font-weight: 600;
    color: var(--black);
    font-size: 1.1em;
}

.gift-size-list {
    display: flex;
    gap: 0.75em;
    flex-wrap: wrap;
}

.gift-size-list .product-size__item {
    font-size: 1.3em;
    width: 3em;
    height: 3em;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: 500;
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    background-color: rgba(var(--rgb-white), 0.3);
    transition: all 0.3s ease;
}

.gift-size-list .product-size__item.product-size--active {
    background-color: var(--primary);
    color: var(--white);
}

.gift-size-list .product-size__item:hover {
    background-color: var(--black);
    color: var(--white);
}

/* Mobile responsive for gift modal */
@media (max-width: 768px) {
    .gift-modal {
        width: 95%;
    }

    .gift-product {
        padding: 2.5em 0.8em 0.8em;
        font-size: 1em;
    }
    .gift-product__info {
        flex-direction: row;
    }
    .gift-product__details {
        flex: 1;
        font-size: 1.25em;
    }
    .gift-product__size-btn {
        width: fit-content;
        font-size: 1em;
    }
    .gift-size-product img {
        width: 6em;
        height: 8em;
    }
}

/* Responsive for Gift Component */
@media (max-width: 768px) {
    .checkout__summary__mobile .gift-product {
        padding: 2.5em 0.8em 0.8em;
        font-size: 1em;
        margin: 1.5em 0;
    }

    .gift-modal {
        width: 95%;
        margin: 1em;
    }

    .gift-modal__header,
    .gift-modal__body,
    .gift-modal__footer {
        padding: 1.5em;
    }

    .gift-size-product img {
        width: 6em;
        height: 8em;
    }
}

/* Gift Cart Item Styling */
.cart__item.gift-item {
    position: relative;
    background: linear-gradient(135deg,
            rgba(255, 107, 157, 0.1) 0%,
            rgba(255, 138, 155, 0.1) 100%);
    border: 1px solid rgba(255, 107, 157, 0.3);
    border-radius: 1em;
    padding: 1em;
    margin-bottom: 1em;
    overflow: hidden;
}

.gift-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
    color: white;
    padding: 0.25em 0.75em;
    border-radius: 0;
    font-size: 0.95em;
    font-weight: 600;
    z-index: 2;
    border-bottom-right-radius: 1em;
}

/* Gift item styling for both sidebar and main cart */
.cart__item.gift-item .cart__item__price--sale {
    color: #ff6b9d !important;
    font-weight: 700 !important;
}

.cart__item.gift-item .cart__item__total {
    color: #ff6b9d !important;
    font-weight: 700 !important;
}

.cart__item.gift-item .product-size__item.product-size--active {
    background-color: #ff6b9d !important;
    border-color: #ff6b9d !important;
    color: white !important;
}

/* Gift Notification */
.gift-notification {
    position: fixed;
    top: 2em;
    right: 2em;
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
    color: white;
    padding: 1em 1.5em;
    border-radius: 0.5em;
    font-weight: 600;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 9999;
}

.gift-notification.show {
    transform: translateX(0);
    opacity: 1;
}

/* Gift Item in Sidebar Cart */
#cartSidebar .cart__item.gift-item {
    background: linear-gradient(135deg,
            rgba(255, 107, 157, 0.1) 0%,
            rgba(255, 138, 155, 0.1) 100%);
    border: 1px solid rgba(255, 107, 157, 0.3);
    border-radius: 1em;
    padding: 1em;
    margin-bottom: 1em;
    overflow: hidden;
}

.gift-item {
    position: relative;
}

.gift-modal .product-size {
  width: 100%;
}


#cartSidebar .gift-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
    color: white;
    padding: 0.25em 0.75em;
    border-radius: 0;
    font-size: 0.95em;
    font-weight: 600;
    border-bottom-right-radius: 1em;
}

#cartSidebar .gift-price-free {
    color: #ff6b9d !important;
    font-weight: 700 !important;
}

/* Gift Item in Order Detail List */
.order-detail__item.gift-item {
    position: relative;
    background: linear-gradient(135deg,
            rgba(255, 107, 157, 0.1) 0%,
            rgba(255, 138, 155, 0.1) 100%);
    border: 1px solid rgba(255, 107, 157, 0.3);
    border-radius: 1em;
    padding: 1em;
    margin-bottom: 1em;
    overflow: hidden;
}

.order-detail__item.gift-item .gift-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8a9b 100%);
    color: white;
    padding: 0.25em 0.75em;
    border-radius: 0;
    font-size: 0.95em;
    font-weight: 600;
    z-index: 2;
    border-bottom-right-radius: 1em;
}

.order-detail__item.gift-item .gift-price-free {
    color: #ff6b9d !important;
    font-weight: 700 !important;
}

/* Gift Item Highlight Animation */
@keyframes giftHighlight {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 107, 157, 0.7);
    }

    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(255, 107, 157, 0.3);
    }

    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 107, 157, 0);
    }
}