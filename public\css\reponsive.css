/* 
* <576:     xs
* 576-768:  sm
* 768-992:  md
* 992-1200: lg
* 1200 -1400: xl
* >= 1400:  xxl
*/
@media (max-width: 768px){  
    :root{
        --10px: 1.5vw;
    }
    .container90{
        width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }
    .header-info-bar, .cate-menus__wrapper, .menus-nav, .header-right{
        display: none;
    }
    .header-right {
        display: flex;
        font-size: 1.2em;
    }
    .wishlist-link {
        display: none;
    }
    .maybi__logo, .menu-mobile-bar{
        display: block;
    }
    .maybi__logo{
        width: 28em;
        height: 8em;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    .header-navbars{
        height: 7.9em;       
    }
    .voucher__wrapper {
        grid-template-columns: repeat(2, 1fr);  
        gap: 1em;
    }
    .vouchers{
        padding-top: 3em;
    }
    .category {
        padding: 2em;
    }
    .categories {      
        grid-template-columns: repeat(5, 20%);
        gap: 2em;
    }
    .category__item {
        font-size: 1.2em;
        gap: 0.8em;
    }
    .category__title {
        font-size: 1em;
    }
    .category__thumb{
        width: 6em;
        height: 6em;
    }
    .category__icon {
        width: 5em;
        height: 4em;
    }
   
    /* --- flashsale --- */
    .flashsale__header {
        height: 10em;
        font-size: var(--10px);
        align-items: center;
    }

    .flashsale__countdown{
        flex-direction: column;
        gap: 0.5em;
        font-size: 1.5em;
        height: auto;
        align-items: flex-start;
    }

    .product-list {        
        grid-template-columns: repeat(2, 1fr);
    }      

    .sm-offcanvas-w100{
        width: 100%;
    }

    .btn-cart{
        font-size: 2.2em;
        padding-top: 1.6em;
        padding-bottom: 1.6em;
    }

    .footer__wrapper{
        grid-template-columns: repeat(2, 1fr);
        gap: 1em;
    }

    .product-color:not(.product-color--more) {
        width: 4.6em;
        height: 3em;
    }

    .product-color__item {
        width: 4.6em;
        height: 3em;
    }

    .product-icon{
        opacity: 1;
        visibility: visible;
        transform: translate(0);
    }

    .product-size{
        opacity: 1;
        transform: translateX(.5em);
        width: 3.5em;
    }

    .product-prices>span {
        font-size: 1.6em;
    }
    .product--p10 {
        padding: 0.5em;
    }
    .product-colors {
        font-size: var(--10px);
        gap: 0.5em;
        font-size: 0.85em;
        flex-wrap: wrap;
    }
    .btn-quick-view-icon {
        display: flex;
    }
    .product-view.btn-quick-view{
        display: none;
    }
    .product-size__item {
        width: 100%;
        aspect-ratio: 1 / 1;
        height: 100%;
        font-size: 1.5em;
    }
    .cart__item__variants .product-size__item {
        width: 3em;
    }
    .m_extra_nav.active{
        bottom: 0;
    }

    .cart__item__variants > .product-size{
        width: 15em;
    }
    .btn-primary {
        font-size: 2vw;
    }
}
@media (max-width: 575px){   
    .xs-hidden{
        display: none;
    }
    .xs-block{
        display: block;
    }
    .xs-news-w100{
        width: 100%;
    }
    .xs-box-w100{
        max-width: 100%;
    }       
    .product__title{
        height: 4.2em;
    }
    .product__title>a {
        font-size: 1.7em;
    } 
    .product-colors {
        font-size: var(--10px);
        flex-wrap: wrap;
    }
    .category {
        font-size: var(--10px);
        padding: 2em;
    }
    .categories {
        grid-template-columns: repeat(3, 33%);
        gap: 2em;
    }
    .categories > :nth-child(10) {
        grid-column: 2;
        justify-self: center;
    }
    .category__title {
        font-size: 2em;
    }
    .category__thumb {
        width: 6em;
        height: 6em;
        font-size: 2em;
    }
    .btn-primary {
        font-size: 3vw;
    }
    .product-size {
        opacity: 1;
        transform: translateX(.5em);
        width: 4.5em;
    }
    .swiper-button-next.flashsale__next,
    .swiper-button-prev.flashsale__prev {
        position: relative;
        width: 6em;
        height: 6em;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, .2);
        font-size: var(--10px);
        margin: 0;
    }
}
@media (min-width: 576px) and (max-width: 768px){
   .product__title{
        height: 3.5em;
    }
    .product__title>a {
        font-size: 1.5em;
    } 
}
    
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  
}

@media only screen and (min-width: 992px) and (max-width: 1199px){
   
    
}

@media only screen and (min-width: 1200px) and (max-width: 1399px){
    
}