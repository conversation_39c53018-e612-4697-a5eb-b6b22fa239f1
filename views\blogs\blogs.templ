package blogs

import (
"github.com/networld-solution/gos/templates"
"goweb/views/blogs/components"
"goweb/views/layouts"
)

templ Blogs() {
	@layouts.Master(nil, &[]templ.Component{headBlogs()}, scriptBlogs()) {
	@components.BlogsBackground()
	@components.BlogsMenuSubCpn()
	@components.BlogsNewCpn()
	@components.BlogsViewsCpn()

	<section class="container90 ana-main">
		<div class="ana-main__wrapper">
			<div class="wrap__left">
				@components.BlogsXuHuongThoiTrangCpn()
				@components.BlogsPhongCachCpn()
			</div>
			<div class="wrap__right">
				@components.BlogRightBannerCpn()
			</div>
		</div>
	</section>

	<section class="container90 library-wrapper" id="library-section">
		<div class="lib-grid-box">
			<span class="lib-grid-box__title">BỘ SƯU TẬP</span>
			<div class="txt-hover-green lib-grid-box__seemore">
				<span>Xem tất cả</span>
				<div class="lib-grid-box__seemore-icon">
					<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
						<use xlink:href="#icon-arrow-right-s"></use>
					</svg>
				</div>
			</div>
		</div>                  
		@components.BlogsBoSuuTapCpn() 
	</section>
}
}

templ headBlogs() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/blogs.css") } />
}

templ scriptBlogs() {
	<script type="text/javascript" src={ templates.AssetURL("/static/js/maybi-ui.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/js/blogs.js") }></script>
	// JS Scroll Section
	<script>
	document.addEventListener('DOMContentLoaded', function () {
		const menuItems = document.querySelectorAll('.menubrc-item');

		menuItems.forEach(item => {
			item.addEventListener('click', function () {
				const targetSelector = this.getAttribute('data-target');
				const targetElement = document.querySelector(targetSelector);

				if (!targetElement) return;

				menuItems.forEach(el => el.classList.remove('active'));
				this.classList.add('active');

				const offset = 140;
				const elementPosition = targetElement.getBoundingClientRect().top + window.scrollY;
				const offsetPosition = elementPosition - offset;

				window.scrollTo({
					top: offsetPosition,
					behavior: 'smooth'
				});

				history.replaceState(null, null, window.location.pathname + window.location.search);
			});
		});
	});
	</script>

	// JS Show More Section
	<script>
		document.addEventListener('DOMContentLoaded', function () {
			// Handle Xu Hướng Thời Trang section
			const xuHuongSection = document.querySelector('#burden-section');
			if (xuHuongSection) {
				const xuHuongArticles = xuHuongSection.querySelectorAll('.ana-articles .nitem');
				const xuHuongBtn = xuHuongSection.querySelector('.btn-show-more');
				const xuHuongMenuLine = xuHuongSection.querySelector('.menu-line');

				// Initially hide articles after index 2 (show first 3)
				xuHuongArticles.forEach((article, index) => {
					if (index >= 3) {
						article.classList.add('hidden');
					}
				});

				// Add click event to show more button
				if (xuHuongBtn) {
					xuHuongBtn.addEventListener('click', function () {
						xuHuongArticles.forEach(article => article.classList.remove('hidden'));
						xuHuongBtn.style.display = 'none';
						xuHuongMenuLine.style.display = 'none';
					});
				}
			}

			// Handle Phong Cách section
			const phongCachSection = document.querySelector('#phong-cach-section');
			if (phongCachSection) {
				const phongCachArticles = phongCachSection.querySelectorAll('.ana-articles .nitem');
				const phongCachBtn = phongCachSection.querySelector('.btn-show-more');
				const phongCachMenuLine = phongCachSection.querySelector('.menu-line');

				// Initially hide articles after index 2 (show first 3)
				phongCachArticles.forEach((article, index) => {
					if (index >= 3) {
						article.classList.add('hidden');
					}
				});

				// Add click event to show more button
				if (phongCachBtn) {
					phongCachBtn.addEventListener('click', function () {
						phongCachArticles.forEach(article => article.classList.remove('hidden'));
						phongCachBtn.style.display = 'none';
						phongCachMenuLine.style.display = 'none';
					});
				}
			}
		});
	</script>
}