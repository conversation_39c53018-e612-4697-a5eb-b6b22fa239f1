<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quick View Modal - Simple</title>
    <link rel="stylesheet" href="public/css/quick-view-modal.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .product { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin: 10px; 
            display: inline-block; 
            width: 300px; 
            background: white;
            border-radius: 8px;
        }
        .product__img { width: 100%; height: 200px; object-fit: cover; border-radius: 4px; }
        .btn-quick-view { 
            background: #007bff; 
            color: white; 
            padding: 8px 16px; 
            border: none; 
            cursor: pointer; 
            margin: 10px 0;
            border-radius: 4px;
            width: 100%;
        }
        .btn-quick-view:hover { background: #0056b3; }
        .product__title a { text-decoration: none; color: #333; font-weight: bold; }
        .product-prices { margin: 10px 0; }
        .sale-price { color: #e74c3c; font-weight: bold; margin-right: 10px; }
        .product-rating { margin: 5px 0; display: flex; align-items: center; gap: 10px; }
        .mb-rating { display: flex; gap: 2px; }
        .mb-start-fill, .mb-start { width: 16px; height: 16px; }
        .product-colors { list-style: none; padding: 0; display: flex; gap: 5px; margin: 10px 0; }
        .product-color__item { width: 20px; height: 20px; border-radius: 50%; display: block; }
        .product-size { list-style: none; padding: 0; display: flex; gap: 5px; margin: 10px 0; }
        .product-size__item { padding: 5px 10px; border: 1px solid #ddd; cursor: pointer; border-radius: 4px; }
        .product-size--active { background: #007bff; color: white; }
        .product-color.active .product-color__item { border: 2px solid #007bff; }
    </style>
</head>
<body>
    <!-- SVG Symbols -->
    <svg aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;">
        <defs>
            <symbol id="icon-star-active" viewBox="0 0 13 12" fill="none">
                <path d="M5.84108 1.25471C6.09463 0.737166 6.83 0.730621 7.09272 1.24357L8.4898 3.97122L11.5505 4.38133C12.1333 4.45942 12.366 5.17773 11.9397 5.58267L9.72789 7.68345L10.2668 10.606C10.3719 11.1764 9.77754 11.6199 9.26068 11.3567L6.45578 9.92806L3.74781 11.3379C3.23076 11.6071 2.63045 11.1634 2.73616 10.5901L3.27211 7.68345L1.06034 5.58267C0.634009 5.17773 0.866671 4.45942 1.44946 4.38133L4.5102 3.97122L5.84108 1.25471Z" fill="#FCC00D" />
            </symbol>
            <symbol id="icon-star-fill" viewBox="0 0 13 13" fill="none">
                <path d="M8.23994 4.58519L8.29958 4.70163L8.42925 4.71901L11.49 5.12911C11.8646 5.17931 12.0142 5.64109 11.7401 5.90141L9.52838 8.00219L9.43012 8.09552L9.45469 8.22879L9.99357 11.1513C10.0612 11.518 9.67906 11.8031 9.3468 11.6339L6.5419 10.2053L6.42718 10.1469L6.31299 10.2063L3.60502 11.6162C3.27263 11.7893 2.88671 11.504 2.95467 11.1355L3.49062 8.22879L3.51519 8.09552L3.41694 8.00219L1.20517 5.90141C0.931097 5.64109 1.08067 5.17931 1.45531 5.12911L4.51606 4.71901L4.64855 4.70126L4.70737 4.58121L6.03824 1.8647C6.20124 1.53199 6.67397 1.52778 6.84287 1.85754L8.23994 4.58519Z" stroke="#FCC00D" stroke-width="0.5"/>
            </symbol>
            <symbol id="icon-up" viewBox="0 0 24 24"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></symbol>
            <symbol id="icon-down" viewBox="0 0 24 24"><path d="M5 11V13H19V11H5Z"></path></symbol>
        </defs>
    </svg>

    <div class="test-container">
        <h1>Test Quick View Modal</h1>
        
        <!-- Test Product -->
        <article class="product" 
            data-description="Áo blazer form rộng với thiết kế hiện đại, phù hợp cho môi trường công sở và dạo phố. Chất liệu cao cấp, thoáng mát và dễ phối đồ."
            data-product-code="BLZ001"
            data-categories="Áo blazer,Công sở,Thời trang nữ"
            data-tags="Mới về,Bán chạy,Công sở">
            
            <div class="product__wrapper">
                <img class="product__img" src="https://via.placeholder.com/300x400/333/fff?text=Blazer+1" alt="Áo blazer form rộng">
                <img class="product__img--hover" src="https://via.placeholder.com/300x400/666/fff?text=Blazer+2" alt="Áo blazer form rộng" style="display: none;">
            </div>
            
            <div class="product__info">
                <h3 class="product__title">
                    <a href="/ao-blazer-form-rong.html" title="Áo blazer form rộng">Áo blazer form rộng</a>
                </h3>
                <div class="product-rating">
                    <div class="mb-rating">
                        <svg class="mb-start-fill"><use href="#icon-star-active"></use></svg>
                        <svg class="mb-start-fill"><use href="#icon-star-active"></use></svg>
                        <svg class="mb-start-fill"><use href="#icon-star-active"></use></svg>
                        <svg class="mb-start-fill"><use href="#icon-star-active"></use></svg>
                        <svg class="mb-start"><use href="#icon-star-fill"></use></svg>
                    </div>
                    <div class="product-rating__selled">Đã bán 200+</div>
                </div>
                <div class="product-prices">
                    <span class="sale-price">1.299.000đ</span>
                    <span>1.390.000đ</span>
                </div>
                <ul class="product-colors">
                    <li class="product-color active" data-img="https://via.placeholder.com/300x400/000/fff?text=Black">
                        <span class="product-color__item" style="background-color: black;"></span>
                    </li>
                    <li class="product-color" data-img="https://via.placeholder.com/300x400/607c2c/fff?text=Green">
                        <span class="product-color__item" style="background-color: #607c2c;"></span>
                    </li>
                </ul>
                <ul class="product-size">
                    <li data-color='[{"color": "black", "img": "https://via.placeholder.com/300x400/000/fff?text=S+Black"}]' class="product-size__item">S</li>
                    <li data-color='[{"color": "black", "img": "https://via.placeholder.com/300x400/000/fff?text=M+Black"}, {"color": "#607c2c", "img": "https://via.placeholder.com/300x400/607c2c/fff?text=M+Green"}]' class="product-size__item product-size--active">M</li>
                    <li data-color='[{"color": "black", "img": "https://via.placeholder.com/300x400/000/fff?text=L+Black"}]' class="product-size__item">L</li>
                </ul>
            </div>
            
            <button class="btn-quick-view" data-product-id="1">Xem nhanh</button>
        </article>
    </div>

    <!-- Quick View Modal -->
    <div class="product-modal product-modal--hidden" id="product-modal">
        <div class="product-modal__overlay"></div>
        <div class="product-modal__box">
            <button class="product-modal__close" id="modal-close">&times;</button>
            <div class="product-modal__body">
                <div class="product-viewer">
                    <div class="product-thumbnails"></div>
                    <div class="swiper swiper-quick-view">
                        <div class="swiper-wrapper"></div>
                    </div>
                </div>
                
                <div class="product-modal__content">
                    <div class="product-modal__content-top product">
                        <div class="product-modal__content-box"></div>
                        <h4 class="product-modal__title">
                            <a href="#">Product Title</a>
                        </h4>
                        <div class="product__quick-meta">
                            <div class="product-rating">
                                <div class="mb-rating"></div>
                                <div class="product-rating__selled">Đã bán 0</div>
                            </div>
                        </div>
                        <p class="product-modal__desc">Product description will be loaded here</p>
                        <div class="product-modal__meta">
                            <div class="product-modal__meta-left">
                                <label class="product-modal__label-text">Số lượng</label>
                                <div class="product-modal__quantity-control quantity-control">
                                    <div class="product-modal__quantity-inline">
                                        <button class="product-modal__btn quantity__btn-down" type="button">
                                            <svg><use href="#icon-down"></use></svg>
                                        </button>
                                        <input class="product-modal__quantity-input quantity-input" type="text" value="1"/>
                                        <button class="product-modal__btn quantity__btn-up" type="button">
                                            <svg><use href="#icon-up"></use></svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="product-modal__meta-right">
                                <label class="product-modal__label-text">Giá</label>
                                <div class="product-modal__price"></div>
                            </div>
                        </div>
                        <div class="product-modal__options product__info">
                            <div class="product-modal__options-left">
                                <label class="product-modal__label-text">Kích thước</label>
                                <ul class="product-size"></ul>
                            </div>
                            <div class="product-modal__options-right">
                                <label class="product-modal__label-text">Màu</label>
                                <ul class="product-colors"></ul>
                            </div>
                        </div>
                        <div class="product-modal__actions">
                            <a href="#" class="btn btn--primary btn_add_to_cart">
                                <span>Thêm vào giỏ</span>
                            </a>
                            <a href="/thanh-toan" class="btn btn--outline">
                                <span>Mua ngay</span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="product-modal__content-bot">
                        <div class="product-modal__info"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script src="public/js/quick_view_modal.js"></script>
</body>
</html>
