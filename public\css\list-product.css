.product {
    overflow: hidden;
}

/* List Product Section */
#banner-product {
    aspect-ratio: 1920 / 300;
    width: 100%;
}

.list-product {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 18.75em;
    position: relative;
    display: flex;
    align-items: center;
    padding: 5em 0;
    font-size: var(--10px);
}

/* .list-product--with-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
} */

.list-product__container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 75em;
    margin: 0 auto;
    padding: 0 0.9375em;
}

/* Product Grid Content */
.list-product__content.list-product__content--grid {
    margin-bottom: 1.875em;
    text-align: left;
    color: inherit;
}

/* Breadcrumb - New position styling */
.list-product__breadcrumb {
    background: var(--white);
    padding: 1.25em 0;
    border-bottom: 0.0625em solid #E5E5E5;
    font-size: 0.652vw;
}

.breadcrumb-list {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-product__breadcrumb .breadcrumb-item {
    color: var(--rgb-black);
    font-size: 1.2em;
    font-weight: 500;
}

.list-product__breadcrumb .breadcrumb-item+.breadcrumb-item::before {
    content: '';
    display: inline-block;
    width: 1.25em;
    height: 1.25em;
    margin: 0 0.625em;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z'%3E%3C/path%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.7;
    vertical-align: middle;
}

.list-product__breadcrumb .breadcrumb-link {
    color: var(--rgb-black);
    text-decoration: none;
    transition: color 0.3s ease;
}

.list-product__breadcrumb .breadcrumb-link:hover {
    color: var(--rgba-primary-8);
    text-decoration: underline;
}

/* List Product Main Section - Merged with hero section */
.list-product.list-product--main {
    padding: 1.875em 0;
    background: none;
    min-height: auto;
}

/* Container styles merged - see base .list-product__container above */

.list-product__layout {
    display: flex;
    gap: 1.875em;
}

/* Sidebar Styles */
.list-product__sidebar {
    flex: 3;
    font-size: var(--10px);
}

.list-product__sidebar-sticky {
    position: sticky;
    top: 1.25em;
    max-height: calc(100vh - 2.5em);
    overflow-y: auto;
    padding-right: 0.5em;
}

.list-product__sidebar-sticky {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.list-product__sidebar-sticky::-webkit-scrollbar {
    display: none;
}

.list-product__close-btn {
    display: none;
    position: absolute;
    top: -2.5em;
    right: 0;
    width: 2.1875em;
    height: 2.1875em;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    z-index: 10;
}

.list-product__close-btn svg {
    width: 1.25em;
    height: 1.25em;
}

.list-product__filter {
    padding: 1.5em;
}

.list-product__filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.875em;
}

.list-product__filter-title {
    font-size: 1.6em;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.list-product__filter-icon {
    margin-right: 0.75em;
    font-size: 1.25em;
    width: 1.25em;
    height: 1.25em;
}

.list-product__filter-icon svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.list-product__panel-icon {
    width: 100%;
    height: 100%;
}

/* Widget Styles */
.list-product__widget {
    margin-bottom: 1.875em;
    transition: all 0.3s ease;
}

.list-product__widget:last-child {
    margin-bottom: 0;
}

.list-product__widget--active {
    padding: 0em;
}

.list-product__widget-title,
.list-product .widget-title {
    font-size: 1.6em;
    font-weight: 600;
    margin-bottom: 1em;
    color: rgba(var(--rgb-black), 0.8);
}

/* Search Widget */
.list-product__widget--search {
    margin-bottom: 1.875em;
}

.list-product__search-form {
    position: relative;
}

.list-product__input-group {
    position: relative;
    display: flex;
}

.list-product__search-input {
    width: 100%;
    padding: 0.75em 3em 0.75em 1em;
    border: 0.0625em solid var(--secondary);
    border-radius: 0.72em;
    outline: none;
    transition: all 0.3s linear;
    font-size: 1.4em;
}

.list-product__search-input:focus {
    border-color: var(--rgba-primary-8);
    box-shadow: var(--rgba-primary-8) 0px 3px 6px;
    /* box-shadow: var(--rgba-primary-8) 0px 1px 4px, var(--rgba-primary-8) 0px 0px 0px 3px; */
}

.list-product__input-addon {
    position: absolute;
    height: 100%;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.list-product__search-btn {
    padding: 0 1em;
    background: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4em;
    height: 4em;
    font-size: 1em;
}

.list-product__search-btn svg {
    width: 100%;
    aspect-ratio: 1;
    fill: var(--white);
}

/* Price Slider */
.list-product__price-slider {
    padding: 1em 0;
}

.list-product__range-slider {
    margin-bottom: 1em;
}

.list-product__slider {
    margin-bottom: 1.875em;
    height: 0.8em;
    background: #FAFAFA;
    border-radius: 0.4em;
    border: 0.1em solid #D3D3D3;
}

/* Price values container */
.list-product__price-values {
    display: flex;
    justify-content: space-between;
    margin-top: 0.75em;
}

/* Price value styling */
.list-product__price-value {
    display: inline-block;
    font-size: 1em;
    color: var(--rgb-black);
    font-weight: 500;
    margin: 0;
    padding: 0.25em 0;
}

/* Price Range Styles - Optimized selectors */
.list-product .price-range {
    margin-top: 1.25em;
}

.list-product .price-inputs {
    display: flex;
    align-items: center;
    gap: 1.75em;
    margin-bottom: 1em;
}

.list-product .price-input-wrapper {
    flex: 1;
    position: relative;
}

.price-input-wrapper:last-child:before {
    content: '-';
    position: absolute;
    left: -1.125em;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-weight: 500;
    font-size: 1em;
}

.list-product .price-input {
    width: 100%;
    padding: 0.75em 1em;
    border: 0.125em solid #E5E5E5;
    border-radius: 0.72em;
    background: var(--white);
    color: var(--black);
    font-size: 1.2em;
    font-weight: 500;
    transition: all 0.2s ease;
    outline: none;
}

.list-product .price-input::placeholder {
    color: var(--light-grey);
    text-transform: uppercase;
    font-size: 0.875em;
    letter-spacing: 0.025em;
    font-weight: 500;
    line-height: 1.2;
}

.list-product .price-input:focus {
    border-color: var(--rgba-primary-8);
    box-shadow: 0 0 0 0.125em rgba(var(--rgba-primary-8), 0.1);
}

.list-product .price-input:hover {
    border-color: var(--rgba-primary-8);
}

.list-product .price-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.list-product .price-tag {
    padding: 0.5em 1em;
    border: 0.0625em solid #E5E5E5;
    border-radius: 0.72em;
    background: #F8F8F8;
    color: var(--light-grey);
    font-size: 1.1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex: 1;
}

.list-product .price-tag:hover {
    border-color: var(--rgba-primary-8);
    background: var(--rgba-primary-1);
    color: var(--rgba-primary-8);
}

.list-product .price-tag--active {
    border-color: var(--rgba-primary-8);
    background: var(--rgba-primary-8);
    color: white;
}

/* Material & Style Filter Styles - Shared */
.material-filter,
.style-filter {
    margin-top: 1.25em;
}

.material-list,
.style-list {
    display: flex;
    flex-direction: column;
    gap: 0.75em;
}

.material-item,
.style-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.material-item:hover,
.style-item:hover {
    color: var(--rgba-primary-8);
}

.material-item.hidden,
.style-item.hidden {
    display: none;
}

.material-input,
.style-input {
    width: 1.125em;
    height: 1.125em;
    margin-right: 0.75em;
    accent-color: var(--rgba-primary-8);
    cursor: pointer;
    font-size: var(--10px);
}

.material-label,
.style-label {
    font-size: 1.2em;
    font-weight: 400;
    color: var(--rgb-black);
    cursor: pointer;
    transition: color 0.2s ease;
}

.material-item:hover .material-label,
.style-item:hover .style-label {
    color: var(--rgba-primary-8);
}

.material-toggle,
.style-toggle {
    margin-left: 1.5em;
    padding: 0.5em 0;
    background: none;
    border: none;
    color: var(--light-grey);
    font-size: 1.2em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.material-toggle:hover,
.style-toggle:hover {
    color: var(--rgba-primary-9);
}

.material-toggle.expanded,
.style-toggle.expanded {
    color: var(--light-grey);
}

.material-toggle.expanded:hover,
.style-toggle.expanded:hover {
    color: var(--rgba-primary-8);
}

/* Color Filter */
.list-product__color-filter {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5em;
    padding-left: 0.25em;
}

.list-product__color-option {
    position: relative;
}

.list-product__color-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.list-product__color-swatch {
    display: block;
    width: 3.5em;
    height: 2.2em;
    border-radius: 1.5em;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
}

.list-product__color-input:checked+.list-product__color-swatch {
    transform: scale(1);
}

.list-product__color-input:checked+.list-product__color-swatch::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4em;
    height: 2.7em;
    border-radius: 1.5em;
    border: solid 1px var(--light-grey);
}

/* Hover effect for better UX */
.list-product__color-swatch:hover {
    transform: scale(1);
}

/* Size Group */
.list-product__size-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-product__size-item {
    font-size: 1em;
    width: 2.5em;
    height: 2.5em;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    border: 0.1em solid rgba(var(--rgb-black), 0.2);
    background-color: rgba(var(--rgb-white), 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    user-select: none;
}

.list-product__size-item--active {
    background-color: var(--primary);
    color: var(--white);
}

.list-product__size-item:hover {
    background-color: var(--black);
    color: var(--white);
}

.list-product__size-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.list-product__size-label {
    display: inline-block;
    padding: 0.5em 0.875em;
    border: 0.0625em solid #ddd;
    border-radius: 0.25em;
    font-size: 0.875em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
    color: #333;
}

.list-product__size-input:checked+.list-product__size-label {
    background: #0D775E;
    color: white;
    border-color: #0D775E;
}

.list-product__size-label:hover {
    border-color: #0D775E;
}

/* Category Widget */
.list-product__widget--categories {
    margin-bottom: 1.875em;
}

.list-product__category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-product__category-item {
    margin-bottom: 0.5em;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.25em 0;
}

.list-product__category-item:last-child {
    margin-bottom: 0;
}

.list-product__category-link {
    color: var(--rgb-black);
    text-decoration: none;
    font-size: 1.2em;
    font-weight: 400;
    transition: color 0.2s ease;
    flex: 1;
}

.list-product__category-link:hover {
    color: var(--primary);
}

.list-product__category-link--active {
    color: #E91E63;
    font-weight: 500;
}

.list-product__category-count {
    font-size: 1em;
    color: var(--rgb-black);
    font-weight: 400;
    margin-left: auto;
}

/* Tags Widget */
.list-product__widget--tags {
    margin-bottom: 1.875em;
}

.list-product__tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75em;
}

.list-product__tag {
    display: inline-block;
    padding: 0.375em 0.75em;
    color: #333;
    text-decoration: none;
    font-size: 1.4em;
    border: 1px solid var(--black);
    border-radius: 0.8em;
    transition: all 0.2s ease;
}

.list-product__tag:hover {
    background: var(--black);
    color: white;
    border-color: var(--black);
}

/* Reset Button */
.list-product__reset-btn {
    display: inline-block;
    padding: 0.75em 1.5em;
    background: var(--black);
    color: white;
    text-decoration: none;
    text-align: center;
    font-size: 1.2em;
    font-weight: 600;
    border-radius: 0.375em;
    transition: all 0.2s ease;
    margin-top: 1.875em;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    z-index: 1;
}

.list-product__reset-btn::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    transition: all .5s;
    opacity: .8;
    transform: translate(-105%);
    border-right: 1px solid rgb(255, 255, 255);
    background-color: rgba(255, 255, 255, .5);
}

.list-product__reset-btn:hover::after {
    opacity: 0;
    transform: translate(0);
}

.list-product__reset-btn:hover {
    background: var(--black);
    color: white;
}

.list-product__reset-btn:hover::after {
    transform: translate(105%);
    opacity: 0;
}



.list-product__filter-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.875em;
    padding: 1.25em 0;
    border-radius: 0.5em;
    flex-wrap: wrap;
    gap: 1em;
}

.list-product__filter-left {
    display: flex;
    align-items: center;
    gap: 1em;
    flex-wrap: wrap;
}

.list-product__filter-right {
    display: flex;
    align-items: center;
    gap: 0.75em;
    flex-wrap: wrap;
}

/* Filter Tags */
.list-product__filter-tags {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.5em;
    flex-wrap: wrap;
}

.list-product__filter-tag {
    margin: 0;
}

.list-product__tag-btn {
    display: flex;
    padding: 0.5em 1em;
    background: transparent;
    color: var(--rgb-black);
    text-decoration: none;
    font-size: 1.4em;
    border: 0.0625em solid black;
    border-radius: 1.08em;
    transition: all 0.2s ease;
    font-weight: 400;
}

.list-product__tag-btn:hover {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.list-product__tag-close {
    font-size: 0.875em;
    opacity: 0.7;
    margin-left: 0.25em;
    width: 1.6em;
    aspect-ratio: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.list-product__tag-close:hover {
    opacity: 1;
}

.list-product__tag-close svg {
    width: 100%;
    height: 100%;
}

.list-product__tag-btn:hover .list-product__tag-close>svg {
    fill: var(--white);
}

.list-product__results-text {
    font-size: 0.875em;
    color: var(--light-grey);
    white-space: nowrap;
}

/* Dropdown Styles */
.list-product__dropdown {
    position: relative;
}

.list-product__dropdown-btn {
    display: flex;
    align-items: center;
    gap: 0.75em;
    padding: 0.75em 1.25em;
    background: transparent;
    border: none;
    border-radius: 0.375em;
    font-size: 1.4em;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 7em 490.721px;
    justify-content: space-between;
    position: relative;
    overflow: hidden;
}

.list-product__dropdown-btn::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    transition: all .5s;
    opacity: .8;
    transform: translate(-105%);
    border-right: 2px solid rgb(255, 255, 255);
    background-color: rgba(255, 255, 255, .5);
    color: var(--black);
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-bottom: 0;
    border-left: .3em solid transparent;
}

.list-product__dropdown-btn:hover {
    border-color: var(--primary);
    box-shadow: 0 0.125em 0.25em rgba(0, 0, 0, 0.1);
}

.list-product__dropdown-btn:hover::after {
    opacity: 0;
    transform: translate(0);
}

.list-product__dropdown-text {
    color: var(--rgb-black);
    font-weight: 500;
    position: relative;
    z-index: 2;
}

.list-product__dropdown-icon {
    width: 1.25em;
    height: 1.25em;
    color: var(--light-grey);
    transition: transform 0.2s ease;
    position: relative;
    z-index: 2;
}

.list-product__dropdown-icon svg {
    width: 100%;
    height: 100%;
}

/* Alternative CSS arrow (if you want to replace SVG) */
.list-product__dropdown-icon--css {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    width: 0;
    height: 0;
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
    transition: transform 0.2s ease;
    position: relative;
    z-index: 2;
}

/* Dropdown active state */
.list-product__dropdown--active .list-product__dropdown-btn {
    border-color: var(--primary);
    box-shadow: 0 0.125em 0.25em rgba(0, 0, 0, 0.1);
}

.list-product__dropdown--active .list-product__dropdown-icon {
    transform: rotate(180deg);
}

/* Dropdown menu */
.list-product__dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 0.0625em solid #ddd;
    border-radius: 0.375em;
    box-shadow: 0 0.25em 0.5em rgba(0, 0, 0, 0.1);
    z-index: 100;
    margin-top: 0.25em;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-0.5em);
    transition: all 0.2s ease;
}

.list-product__dropdown--active .list-product__dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.list-product__dropdown-item {
    padding: 0.75em 1.25em;
    color: var(--rgb-black);
    text-decoration: none;
    display: block;
    transition: background-color 0.2s ease;
    font-size: 1em;
}

.list-product__dropdown-item:hover {
    background-color: #f8f9fa;
}

.list-product__dropdown-item--active {
    background-color: var(--primary);
    color: white;
}

.list-product__dropdown-item--active:hover {
    background-color: var(--primary);
    color: white;
}

/* View Tabs */
.list-product__view-tabs {
    display: flex;
}

.list-product__tab-nav {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    border: 0.0625em solid #ddd;
    border-radius: 0.25em;
    overflow: hidden;
}

.list-product__tab-item {
    margin: 0;
}

.list-product__tab-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5em;
    height: 2.5em;
    background: #fff;
    border-right: 0.0625em solid #ddd;
    transition: background-color 0.2s ease;
    text-decoration: none;
}

.list-product__tab-item:last-child .list-product__tab-link {
    border-right: none;
}

.list-product__tab-link:hover {
    background: #f5f5f5;
}

.list-product__tab-link--active {
    background: #0D775E;
}

.list-product__tab-link svg {
    width: 1em;
    height: 1em;
    fill: #666;
    transition: fill 0.2s ease;
}

.list-product__tab-link:hover svg {
    fill: #333;
}

.list-product__tab-link--active svg {
    fill: white;
}

/* Product Grid - Merged with hero content */

.list-product__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(25em, 1fr));
    gap: 1.875em;
    transition: all 0.3s ease;
}

/* Load More Section */
.list-product__load-more {
    width: 23.125em;
    text-align: center;
    position: relative;
    font-size: 1vw;
    aspect-ratio: 10 / 1;
    margin: 2em auto 0;
}

.load-more__spinner {
    position: absolute;
    bottom: 0.1em;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
}

.load-more__loading {
    visibility: initial;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z' opacity='.25'/%3E%3Cpath d='M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z' fill='%23FF379B'/%3E%3C/svg%3E") no-repeat center;
    border: 0;
    transition-property: transform;
    animation-name: loading;
    animation-duration: 1.2s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    height: 2.25em;
    width: 2.25em;
    margin: 0 auto;
}

.load-more__btn {
    width: 100%;
    height: 100%;
    font-size: 1em;
    padding: 0.625em 0.875em;
    background: var(--white);
    border-radius: 0.5em;
    border: 0.09375em solid var(--rgba-primary-8);
    justify-content: center;
    align-items: center;
    gap: 0.625em;
    display: inline-flex;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more__btn:hover {
    background: var(--rgba-primary-8);
}

.load-more__btn:hover .load-more__text {
    color: var(--white);
}

.load-more__text {
    color: var(--rgba-primary-8);
    font-weight: 500;
    text-align: center;
    text-transform: capitalize;
    line-height: 0.9375em;
    word-wrap: break-word;
    font-size: 0.9375em;
    transition: color 0.3s ease;
}

/* Hidden utility class */
.hidden {
    display: none !important;
}

/* Collection Description */
.collection__description {
    padding: 0;
    border-radius: 0.5em;
    position: relative;
    font-size: var(--10px);
    margin-top: 3.5em;
}

.collection__description::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 20.25em;
    width: 100%;
    background: linear-gradient(to top, rgba(var(--bs-light-rgb), 1), transparent);
    border-radius: 0 0 0.5em 0.5em;
    pointer-events: none;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.collection__description.active::after {
    opacity: 0;
}

.collection__title {
    font-size: 1.8em;
    font-weight: 700;
    color: var(--rgb-black);
    text-align: center;
    margin: 0 0 1.25em;
    line-height: 1.4;
}

.collection__intro {
    font-size: 1.6em;
    color: var(--rgb-black);
    margin: 0 0 2em;
    line-height: 1.6;
}

.collection__content {
    margin-bottom: 2em;
}

.collection__subtitle {
    font-size: 1.6em;
    font-weight: 600;
    color: var(--rgb-black);
    margin: 1.5em 0 0.75em;
    line-height: 1.4;
}

.collection__subtitle:first-child {
    margin-top: 0;
}

.collection__text {
    font-size: 1.6em;
    color: var(--rgb-black);
    margin: 0 0 1em;
    line-height: 1.6;
}

.collection__more {
    text-align: center;
    position: absolute;
    z-index: 2;
    width: 100%;
}

.collection__btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75em 2em;
    background: var(--rgba-primary-8);
    color: var(--white);
    border: none;
    border-radius: 0.25em;
    font-size: 1.25em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.collection__btn:hover {
    background: var(--rgba-primary-8);
    transform: translateY(-0.125em);
}

.collection__btn-text {
    color: inherit;
}

/* Loading Animation */
@keyframes loading {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Grid View Variations */
.list-product__grid--list {
    grid-template-columns: 1fr;
    gap: 1.25em;
}

.list-product__grid--grid-2 {
    grid-template-columns: repeat(auto-fill, minmax(20em, 1fr));
    gap: 1.5em;
}

.list-product__grid--grid-3 {
    grid-template-columns: repeat(auto-fill, minmax(18.75em, 1fr));
    gap: 1.875em;
}

/* Mobile Sidebar */
/*
.list-product__sidebar--open {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: white;
    z-index: 1000;
    overflow-y: auto;
    transform: translateX(0);
    transition: transform 0.3s ease;
}

.list-product__close-btn {
    display: none;
}

*/

/* Body scroll lock when sidebar is open */
body.sidebar-open {
    overflow: hidden;
}

/* Sidebar backdrop */
.list-product__sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
}

/* Show backdrop when sidebar is open */
.list-product__sidebar-backdrop--active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Pagination - Removed */

/* Main Content Area - Merged with previous definition */
.list-product__main {
    flex: 1;
    min-width: 0;
}

/* Responsive override - moved to main responsive section below */

.list-product__filter-left {
    display: flex;
    align-items: center;
    gap: 1em;
    flex-wrap: wrap;
}

.list-product__filter-right {
    display: flex;
    align-items: center;
    gap: 0.75em;
    flex-wrap: wrap;
}



.list-product__results-text {
    font-size: 1.2em;
    color: var(--color-body);
    white-space: nowrap;
}

/* Panel Button for Mobile Filter */
.list-product__panel-btn {
    display: none;
    color: var(--rgb-black);
    font-weight: 500;
    text-decoration: none;
    align-items: center;
    gap: 0.75em;
    padding: 0.75em 1.25em;
    background: transparent;
    border: none;
    border-radius: 0.375em;
    font-size: 1.4em;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 7em;
    justify-content: space-between;
    position: relative;
    overflow: hidden;
}

.list-product__panel-btn::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    transition: all .5s;
    opacity: .8;
    transform: translate(-105%);
    border-right: 2px solid rgb(255, 255, 255);
    background-color: rgba(255, 255, 255, .5);
    color: var(--black);
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-bottom: 0;
    border-left: .3em solid transparent;
}

.list-product__panel-btn:hover {
    border-color: var(--primary);
    box-shadow: 0 0.125em 0.25em rgba(0, 0, 0, 0.1);
}

.list-product__panel-btn:hover::after {
    opacity: 0;
    transform: translate(0);
}

.list-product__panel-icon {
    width: 1.25em;
    height: 1.25em;
}

/* Responsive  */
@media (max-width: 575.98px) {
    .list-product {
        padding: 2.5em 0 1.25em;
        min-height: 12.5em;
        font-size: 2vw;
    }

    .list-product__breadcrumb {
        font-size: 2vw;
    }

    .material-input,
    .style-input {
        font-size: 2vw;
        width: 1.5em;
        height: 1.5em;
    }

    .material-label,
    .style-label {
        font-size: 1.4em;
    }

    .list-product__container {
        padding: 0 0.625em;
    }

    .list-product__layout {
        flex-direction: column;
        gap: 1em;
    }

    .list-product__panel-btn {
        display: flex;
    }

    .list-product__sidebar {
        width: 100vw;
        max-width: 100%;
        order: 2;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        background: white;
        z-index: 1000;
        overflow-y: auto;
        padding: 1.25em;
        box-sizing: border-box;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        box-shadow: 0.25em 0 1em rgba(0, 0, 0, 0.1);
        font-size: 2.5vw;
    }

    .list-product__sidebar--open {
        transform: translateX(0);
    }

    .list-product__close-btn {
        display: flex;
        position: absolute;
        top: 1.25em;
        right: 1.25em;
        width: 2.5em;
        height: 2.5em;
        background: var(--rgba-primary-8);
        border: none;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 1.25em;
        color: white;
        transition: background-color 0.2s ease;
        z-index: 10;
    }

    /* Fix price values in mobile sidebar */
    .list-product__sidebar .list-product__price-values {
        display: flex;
        justify-content: space-between;
        margin-top: 0.75em;
        flex-wrap: wrap;
        gap: 0.5em;
    }

    .list-product__sidebar .list-product__price-value {
        font-size: 0.875em;
        color: var(--rgb-black);
        font-weight: 500;
        margin: 0;
        padding: 0.25em 0;
        flex: 1;
        min-width: 0;
    }

    .list-product__main {
        order: 1;
        width: 100%;
    }

    .list-product__grid {
        grid-template-columns: 1fr 1fr;
        gap: 1em;
    }

    .list-product__filter-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
        padding: 1em 0;
    }

    .list-product__filter-left {
        order: 2;
    }

    .list-product__filter-right {
        order: 1;
        justify-content: space-between;
        flex-direction: row-reverse;
    }

    .list-product__filter {
        padding: 1em;
    }

    .list-product__category-link,
    .list-product__search-input {
        font-size: 1.4em;
    }


    .list-product__load-more {
        width: 45%;
        margin: 1.5em auto;
        font-size: 2em;
    }


    .pagination {
        flex-direction: column;
        gap: 1em;
        text-align: center;
        padding: 1em;
        font-size: 2em;
    }

    .pagination__info {
        flex: none;
    }

    .pagination__text {
        font-size: 0.8em;
    }

    .pagination__link {
        font-size: 0.8em;
    }

    .pagination__icon {
        width: 0.8em;
        height: 0.8em;
    }

    .pagination__dots-icon {
        width: 0.8em;
        height: 0.8em;
    }

    .list-product__dropdown-btn,
    .list-product__panel-btn {
        border-color: var(--primary);
        box-shadow: 0 0.125em 0.25em rgba(0, 0, 0, 0.1);
    }

    .collection__description {
        padding: 1em 0 5em;
    }
}

@media (min-width: 576px) and (max-width: 767.98px) {
    .list-product {
        padding: 2.5em 0 1.25em;
        min-height: 12.5em;
        font-size: 2.25vw;
    }

    .list-product__breadcrumb {
        font-size: 1.75vw;
    }

    .list-product__container {
        padding: 0 0.625em;
    }

    .list-product__layout {
        flex-direction: column;
        gap: 1em;
    }

    .list-product__panel-btn {
        display: flex;
    }

    .list-product__dropdown-btn,
    .list-product__panel-btn {
        border-color: var(--primary);
        box-shadow: 0 0.125em 0.25em rgba(0, 0, 0, 0.1);
    }

    .list-product__sidebar {
        width: 80vw;
        max-width: 100%;
        order: 2;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        background: white;
        z-index: 1000;
        overflow-y: auto;
        padding: 1.25em;
        box-sizing: border-box;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        box-shadow: 0.25em 0 1em rgba(0, 0, 0, 0.1);
        font-size: 2.25vw;
    }

    .list-product__sidebar--open {
        transform: translateX(0);
    }

    .list-product__close-btn {
        display: flex;
        position: absolute;
        top: 1.25em;
        right: 1.25em;
        width: 2.5em;
        height: 2.5em;
        background: var(--rgba-primary-8);
        border: none;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 1.25em;
        color: white;
        transition: background-color 0.2s ease;
        z-index: 10;
    }

    /* Fix price values in mobile sidebar */
    .list-product__sidebar .list-product__price-values {
        display: flex;
        justify-content: space-between;
        margin-top: 0.75em;
        flex-wrap: wrap;
        gap: 0.5em;
    }

    .list-product__sidebar .list-product__price-value {
        font-size: 0.875em;
        color: var(--rgb-black);
        font-weight: 500;
        margin: 0;
        padding: 0.25em 0;
        flex: 1;
        min-width: 0;
    }

    .list-product__main {
        order: 1;
        width: 100%;
    }

    .list-product__grid {
        grid-template-columns: 1fr 1fr;
        gap: 1em;
    }

    .list-product__filter-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 1em;
        padding: 1em 0;
    }

    .list-product__filter-left {
        order: 2;
    }

    .list-product__filter-right {
        order: 1;
        justify-content: space-between;
        flex-direction: row-reverse;
    }

    .list-product__filter {
        padding: 1em;
    }

    .list-product__category-link,
    .list-product__search-input {
        font-size: 1.4em;
    }

    .pagination {
        flex-direction: column;
        gap: 1em;
        text-align: center;
        padding: 1em;
        font-size: 1.5em;
    }

    .pagination__info {
        flex: none;
    }

    .pagination__text {
        font-size: 0.85em;
    }

    .pagination__link {
        min-width: 2.2em;
        height: 2.2em;
        font-size: 0.85em;
    }

    .pagination__icon {
        width: 0.85em;
        height: 0.85em;
    }

    .pagination__dots-icon {
        width: 0.85em;
        height: 0.85em;
    }

    .list-product__load-more {
        width: 60%;
        margin: 1.5em auto;
        font-size: 1.5em;
    }

    .load-more__btn {
        padding: 0.75em 1em;
        font-size: 1em;
    }

    .collection__description {
        margin: 2em auto 0;
        padding: 0.5em 0 4.5em;
        font-size: 0.825em;
    }

    .collection__title {
        font-size: 1.1em;
    }

    .collection__intro,
    .collection__text {
        font-size: 0.9em;
    }

    .collection__subtitle {
        font-size: 0.95em;
    }

    .collection__description::after {
        height: 15em;
    }

    .collection__btn {
        font-size: 1em;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    .list-product {
        padding: 3.75em 0 1.875em;
        font-size: 1.25vw;
    }

    .list-product__breadcrumb {
        font-size: 1.25vw;
    }

    .list-product__layout {
        flex-direction: column;
        gap: 1.875em;
    }

    .list-product__sidebar {
        flex: none;
        font-size: 1.25vw;
        width: 50vw;
        max-width: 85%;
        order: 2;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        background: rgba(var(--bs-light-rgb), 1);
        z-index: 1000;
        overflow-y: auto;
        padding: 1.25em;
        box-sizing: border-box;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        box-shadow: 0.25em 0 1em rgba(0, 0, 0, 0.1);
    }

    .list-product__sidebar--open {
        transform: translateX(0);
    }

    .list-product__close-btn {
        display: flex;
        position: absolute;
        top: 1.25em;
        right: 1.25em;
        width: 2.5em;
        height: 2.5em;
        background: var(--rgba-primary-8);
        border: none;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 1.25em;
        color: white;
        transition: background-color 0.2s ease;
        z-index: 10;
    }

    /* Fix price values in tablet sidebar */
    .list-product__sidebar .list-product__price-values {
        display: flex;
        justify-content: space-between;
        margin-top: 0.75em;
        flex-wrap: wrap;
        gap: 0.5em;
    }

    .list-product__sidebar .list-product__price-value {
        font-size: 0.875em;
        color: var(--rgb-black);
        font-weight: 500;
        margin: 0;
        padding: 0.25em 0;
        min-width: 0;
        text-align: left;
    }

    .list-product__main {
        order: 1;
    }

    .list-product__grid {
        grid-template-columns: 1fr 1fr;
        gap: 1em;
    }

    .list-product__filter-wrapper {
        flex-direction: row;
        align-items: center;
        gap: 1em;
        padding: 1.25em 0;
    }

    .list-product__filter-left {
        order: 1;
    }

    .list-product__filter-right {
        order: 2;
        justify-content: flex-end;
        flex-direction: row-reverse;
    }

    .list-product__dropdown-btn,
    .list-product__panel-btn {
        border-color: var(--primary);
        box-shadow: 0 0.125em 0.25em rgba(0, 0, 0, 0.1);
    }

    /* Show panel button on tablet */
    .list-product__panel-btn {
        display: flex;
    }

    .list-product__load-more {
        width: 45%;
        margin: 2.5em auto;
        font-size: 1.4em;
    }

    .load-more__btn {
        padding: 0.8em 1.2em;
        font-size: 1.4em;
    }

    .collection__description {
        margin: 2.5em auto 0;
        padding: 2em 0 5em;
        font-size: 1.4vw;
    }

    .collection__title {
        font-size: 1.15em;
    }

    .collection__intro,
    .collection__text {
        font-size: 0.95em;
    }

    .collection__subtitle {
        font-size: 1em;
    }

    .collection__description::after {
        height: 18.5em;
    }

    .collection__btn {
        font-size: 1em;
    }
}

@media (min-width: 992px) {
    .list-product__layout {
        flex-direction: row;
        gap: 1.25em;
    }

    .list-product__sidebar {
        width: 16.25em;
        order: 1;
        position: static;
        transform: none;
        transition: none;
        box-shadow: none;
        height: auto;
        z-index: auto;
        flex: 3;
    }

    /* Hide panel button on desktop */
    .list-product__panel-btn {
        display: none;
    }

    .list-product__main {
        flex: 9;
        order: 2;
    }

    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(20em, 1fr));
        gap: 1.25em;
    }
}

@media (min-width: 1199px) {
    .list-product__main {
        flex: 9;
    }

    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(16.25em, 1fr));
        gap: 1.5em;
    }
}

@media (min-width: 1400px) {
    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(20em, 1fr));
        gap: 1.875em;
    }
}

@media (min-width: 1600px) {
    .list-product__container {
        max-width: 90em;
    }

    .list-product__grid {
        grid-template-columns: repeat(auto-fill, minmax(20em, 1fr));
        gap: 2em;
    }
}