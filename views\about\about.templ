package about

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/about/components"
	"goweb/views/layouts"
)

templ About() {
	@layouts.Master(nil, &[]templ.Component{headAbout()}, scriptAbout()) {
		@components.AboutBackground()
		@components.AboutContent()
	}
}

templ headAbout() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/about.css") }/>
}

templ scriptAbout() {
	<script type="text/javascript" src={templates.AssetURL("/static/js/maybi-ui.js")}></script>
	<script type="text/javascript" src={templates.AssetURL("/static/js/about.js")}></script>
}
