/* Contact Page Styles */

/* Breadcrumb Section */
.contact-breadcrumb {
  background: var(--white);
  padding: 1.25em 0;
  border-bottom: 0.0625em solid #e5e5e5;
  font-size: 0.652vw;
}

.contact-breadcrumb .list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}

.contact-breadcrumb .item {
  color: var(--rgb-black);
  font-size: 1.2em;
  font-weight: 500;
}

.contact-breadcrumb .item+.item::before {
  content: "";
  display: inline-block;
  width: 1.25em;
  height: 1.25em;
  margin: 0 0.625em;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
  vertical-align: middle;
}

.contact-breadcrumb .link {
  color: var(--rgb-black);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-breadcrumb .link:hover {
  color: var(--rgba-primary-8);
  text-decoration: underline;
}

/* Contact Page Content */
.contact-page {
  padding: 4em 0;
  background: var(--white);
  font-size: 0.652vw;
}

.contact-page__content {
  margin: 0 auto;
  max-width: 120em;
}

/* Contact Info Section */
.contact-info {
  text-align: center;
  margin-bottom: 4em;
}

.contact-info__title {
  font-size: 2.4em;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 3em 0;
  line-height: 1.3;
}

.contact-methods {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2em;
  max-width: 120em;
  margin: 0 auto;
}

.contact-method {
  background: #f8f9fa;
  padding: 2.5em 2em;
  border-radius: 1em;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-method:hover {
  transform: translateY(-0.3em);
  box-shadow: 0 1em 3em rgba(0, 0, 0, 0.1);
}

.contact-method__icon {
  margin-bottom: 1.5em;
  color: var(--primary);
}

.contact-method__icon svg {
  width: 3em;
  height: 3em;
}

.contact-method__link {
  font-size: 1.6em;
  font-weight: 600;
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-method__link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* Social Links Block */
.contact-method--social {
  text-align: left;
}

.social-links {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1.5em;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 1em;
}

.social-link__icon {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--primary);
}

.social-link__icon svg {
  width: 2.4em;
  height: 2.4em;
}

.social-link__text {
  font-size: 1.4em;
  font-weight: 500;
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
  word-break: break-all;
}

.social-link__text:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* Contact Form Section */
.contact-form-section {
  background: #f8f9fa;
  padding: 4em 3em;
  border-radius: 1.5em;
  max-width: 60em;
  margin: 0 auto;
}

.contact-form__title {
  font-size: 2.4em;
  font-weight: 700;
  color: var(--text-dark);
  text-align: center;
  margin: 0 0 3em 0;
  letter-spacing: 0.1em;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2em;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-input,
.form-textarea {
  padding: 1.2em 1.5em;
  border: 0.1em solid #e0e0e0;
  border-radius: 0.8em;
  font-size: 1.4em;
  font-family: inherit;
  background: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2em rgba(var(--primary-rgb), 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #999;
  font-weight: 400;
}

.form-textarea {
  resize: vertical;
  min-height: 8em;
  line-height: 1.6;
}

.form-submit {
  background: var(--primary);
  color: var(--white);
  border: none;
  padding: 1em 2em;
  border-radius: 0.8em;
  font-size: 1.6em;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
  align-self: center;
  min-width: 15em;
}

.form-submit:hover {
  background: var(--primary-hover);
  transform: translateY(-0.1em);
}

.form-submit:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1400px) {
  .contact-info__title {
    font-size: 2.2em;
  }

  .contact-form__title {
    font-size: 2.2em;
  }

  .contact-methods {
    max-width: 120em;
  }

  .contact-form-section {
    max-width: 55em;
  }
}

@media (max-width: 1200px) {
  .contact-info__title {
    font-size: 2em;
  }

  .contact-form__title {
    font-size: 2em;
  }

  .contact-methods {
    max-width: 120em;
    gap: 1.8em;
  }

  .contact-method {
    padding: 2.2em 1.8em;
  }

  .contact-method__icon svg {
    width: 2.8em;
    height: 2.8em;
  }

  .contact-method__link {
    font-size: 1.5em;
  }

  .social-link__icon svg {
    width: 2.2em;
    height: 2.2em;
  }

  .social-link__text {
    font-size: 1.3em;
  }

  .contact-form-section {
    max-width: 50em;
    padding: 3.5em 2.5em;
  }
}

@media (max-width: 992px) {
  .contact-methods {
    grid-template-columns: repeat(2, 1fr);
    max-width: 70em;
    gap: 1.5em;
  }

  .contact-method {
    padding: 2em 1.5em;
  }

  .contact-method__icon svg {
    width: 2.5em;
    height: 2.5em;
  }

  .contact-method__link {
    font-size: 1.4em;
  }

  .social-link__icon svg {
    width: 2em;
    height: 2em;
  }

  .social-link__text {
    font-size: 1.2em;
  }

  .contact-form-section {
    padding: 3em 2em;
  }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .contact-breadcrumb {
    font-size: 1.75vw;
  }

  .contact-methods {
    grid-template-columns: 1fr;
    max-width: 50em;
  }
}

@media (max-width: 768px) {
  .contact-methods {
    grid-template-columns: 1fr;
    gap: 1.5em;
    max-width: 95%;
  }

  .contact-method {
    padding: 2em 1.5em;
  }

  .social-link__text {
    font-size: 1.2em;
  }
}

@media (max-width: 768px) {
  .contact-page {
    padding: 2em 0;
    font-size: 1.25vw;
  }

  .contact-breadcrumb {
    font-size: 1.5vw;
  }

  .contact-page__content {
    max-width: 100%;
  }

  .contact-info__title {
    font-size: 1.8em;
    margin-bottom: 2em;
  }

  .contact-form-section {
    padding: 2.5em 1.8em;
    max-width: 95%;
  }

  .contact-form__title {
    font-size: 1.8em;
    margin-bottom: 2em;
  }

  .contact-method {
    padding: 1.8em 1.3em;
  }

  .contact-method__icon svg {
    width: 2.2em;
    height: 2.2em;
  }

  .contact-method__link {
    font-size: 1.3em;
  }

  .social-link__icon svg {
    width: 1.8em;
    height: 1.8em;
  }

  .social-link__text {
    font-size: 1.1em;
  }

  .form-input,
  .form-textarea {
    font-size: 1.3em;
    padding: 1em 1.2em;
  }

  .form-submit {
    font-size: 1.4em;
    padding: 1.2em 2.5em;
  }
}

@media (max-width: 575.98px) {
  .contact-page {
    padding: 2em 0;
    font-size: 2vw;
  }

  .contact-info__title {
    font-size: 1.6em;
    margin-bottom: 1.8em;
  }

  .contact-form__title {
    font-size: 1.6em;
    margin-bottom: 1.8em;
  }

  .contact-form-section {
    padding: 2em 1.2em;
    max-width: 98%;
  }

  .contact-method {
    padding: 1.5em 1em;
  }

  .contact-methods {
    gap: 1.2em;
  }

  .contact-method__icon svg {
    width: 2.8em;
    height: 2.8em;
  }

  .contact-method__link {
    font-size: 1.3em;
  }

  .social-link__icon svg {
    width: 1.6em;
    height: 1.6em;
  }

  .social-link__text {
    font-size: 1.2em;
  }

  .social-links {
    gap: 1.2em;
  }

  .social-link {
    gap: 0.8em;
  }

  .form-input,
  .form-textarea {
    font-size: 1.2em;
    padding: 0.9em 1em;
  }

  .form-submit {
    font-size: 1.3em;
    padding: 1em 2em;
    min-width: 12em;
  }
}

@media (max-width: 480px) {
  .contact-page {
    font-size: 2.5vw;
    padding: 1.5em 0;
  }

  .contact-info__title {
    font-size: 1.2em;
    margin-bottom: 1.5em;
  }

  .contact-form__title {
    font-size: 1.3em;
    margin-bottom: 1.5em;
  }

  .contact-form-section {
    padding: 1.5em 1em;
  }

  .contact-method {
    padding: 1.2em 0.8em;
  }

  .contact-method__icon svg {
    width: 2.5em;
    height: 2.5em;
  }

  .contact-method__link {
    font-size: 1em;
    word-break: break-all;
  }

  .social-link__icon svg {
    width: 1.4em;
    height: 1.4em;
  }

  .social-link__text {
    font-size: 0.9em;
    word-break: break-all;
  }

  .social-links {
    gap: 1em;
  }

  .social-link {
    gap: 0.6em;
  }

  .form-input,
  .form-textarea {
    font-size: 1.1em;
  }

  .form-submit {
    font-size: 1.2em;
  }
}