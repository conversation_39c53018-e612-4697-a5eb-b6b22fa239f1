package components

import "github.com/networld-solution/gos/templates"

templ SlideshowHome() {
	<section class="banner-slides">
		<div class="swiper-wrapper">
			<div class="swiper-slide banner-item">
				<a href="#">
					<picture>
						<source
							media="(min-width: 1200px)"
							srcset={ templates.AssetURL("/static/images/slideshow/slider_1.webp") }
							type="image/webp"
						/>
						<source
							media="(min-width: 768px)"
							srcset={ templates.AssetURL("/static/images/slideshow/slider_1_medium.webp") }
							type="image/webp"
						/>
						<source
							media="(max-width: 767px)"
							srcset={ templates.AssetURL("/static/images/slideshow/slider_1_small.webp") }
							type="image/webp"
						/>
						<img
							class="slideshow__img"
							src={ templates.AssetURL("/static/images/slideshow/slider_1.webp") }
							alt="slideshow 1"
						/>
					</picture>
				</a>
			</div>
			<div class="swiper-slide banner-item">
				<a href="#">
					<picture>
						<source
							media="(min-width: 1200px)"
							srcset={ templates.AssetURL("/static/images/slideshow/slider_2.webp") }
							type="image/webp"
						/>
						<source
							media="(min-width: 768px)"
							srcset={ templates.AssetURL("/static/images/slideshow/slider_2_medium.webp") }
							type="image/webp"
						/>
						<source
							media="(max-width: 767px)"
							srcset={ templates.AssetURL("/static/images/slideshow/slider_2_small.webp") }
							type="image/webp"
						/>
						<img
							class="slideshow__img"
							src={ templates.AssetURL("/static/images/slideshow/slider_2.webp") }
							alt="slideshow 2"
						/>
					</picture>
				</a>
			</div>
		</div>
		<div class="group-btn-slide">
			<div class="swiper-banner-button-prev swiper-banner-button">
				<svg>
					<use xlink:href="#arrow-swiper"></use>
				</svg>
			</div>
			<div class="swiper-banner-button-next swiper-banner-button">
				<svg>
					<use xlink:href="#arrow-swiper"></use>
				</svg>
			</div>
		</div>
		<div class="swiper-pagination"></div>
	</section>
}
