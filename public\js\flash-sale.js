document.addEventListener("DOMContentLoaded", function (event) {
    maybiUI.flashSale();
});

var flashsaleSwiper = new Swiper(".flashsale__swiper", {
    loop: true,
    autoplay: {
        delay: 3000,
        disableOnInteraction: true,
        pauseOnMouseEnter: true,
    },
    navigation: {
        nextEl: ".flashsale__next",
        prevEl: ".flashsale__prev"
    },
    spaceBetween: 0,
    breakpoints: {
        320: {
            slidesPerView: 2,
            spaceBetween: 0,
        },
        768: {
            slidesPerView: 3,
        },
        1024: {
            slidesPerView: 5,
        },
    },
    on: {
        // Stop autoplay on any user interaction
        touchStart: function () {
            this.autoplay.stop();
        },
        slideChange: function () {
            if (this.touches.diff !== 0 || this.clickedSlide) {
                this.autoplay.stop();
            }
        },
        navigationNext: function () {
            this.autoplay.stop();
        },
        navigationPrev: function () {
            this.autoplay.stop();
        },
        paginationClick: function () {
            this.autoplay.stop();
        }
    }
});

// Additional interaction handlers for flashsale swiper
document.addEventListener("DOMContentLoaded", function () {
    const flashsaleContainer = document.querySelector('.flashsale__swiper');

    if (flashsaleContainer) {
        // Stop autoplay on hover over any slide
        flashsaleContainer.addEventListener('mouseenter', function () {
            if (flashsaleSwiper && flashsaleSwiper.autoplay) {
                flashsaleSwiper.autoplay.stop();
            }
        });

        // Stop autoplay on any click within the swiper
        flashsaleContainer.addEventListener('click', function () {
            if (flashsaleSwiper && flashsaleSwiper.autoplay) {
                flashsaleSwiper.autoplay.stop();
            }
        });

        // Stop autoplay on keyboard interaction
        flashsaleContainer.addEventListener('keydown', function (e) {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight' || e.key === 'Tab') {
                if (flashsaleSwiper && flashsaleSwiper.autoplay) {
                    flashsaleSwiper.autoplay.stop();
                }
            }
        });
    }
});

var quickViewSwiper = new Swiper(".swiper-quick-view", {
    loop: true,
    spaceBetween: 8,
    breakpoints: {
        768: {
            slidesPerView: 1,
        },
    },
});
