package components

import (
    "github.com/networld-solution/gos/templates"
)

templ QuickViewModal() {
<div class="product-modal product-modal--hidden" id="product-modal">
  <div class="product-modal__overlay"></div>
  <div class="product-modal__box">
    <button class="product-modal__close" id="modal-close">&times;</button>
    <div class="product-modal__body">
        <div class="product-viewer">
            <div class="product-thumbnails">
                <img src={templates.AssetURL("/static/images/dam-om-thun.webp")} data-slide="0" />
                <img src={templates.AssetURL("/static/images/dam-om-thun-2.webp")} data-slide="1" />
                <img src={templates.AssetURL("/static/images/dam-om-thun-3.webp")} data-slide="2" />
            </div>

            <div class="swiper swiper-quick-view swiper-initialized swiper-horizontal">
                <div class="swiper-wrapper">
                    <div class="swiper-slide" style="width: 100%; margin-right: 1.2em;">
                        <div class="product__box product__style wow fadeInUp will-animate">
                            <div class="product__thumb-img">
                                <img src={templates.AssetURL("/static/images/dam-om-thun.webp")} alt="Sale Up to 50% Off" />
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide" style="width: 100%; margin-right: 1.2em;">
                        <div class="product__box product__style wow fadeInUp will-animate">
                            <div class="product__thumb-img">
                                <img src={templates.AssetURL("/static/images/dam-om-thun-2.webp")} alt="Sale Up to 50% Off" />
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide" style="width: 100%; margin-right: 1.2em;">
                        <div class="product__box product__style wow fadeInUp will-animate">
                            <div class="product__thumb-img">
                                <img src={templates.AssetURL("/static/images/dam-om-thun-3.webp")} alt="Sale Up to 50% Off" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="product-modal__content">
            <div class="product-modal__content-top product">
                <div class="product-modal__content-box">
                    <span class="product-modal__label product-modal__sale-percent">-20%</span>
                    <span class="product-modal__label product-modal__sale-hot">hot</span>
                    <span class="product-modal__label">Mới về</span>
                </div>
                <h4 class="product-modal__title">
                    <a href="#">Đầm ôm thun rayon rút nhún dáng midi</a>
                </h4>
                <div class="product__quick-meta">
                    <div class="product-rating">
                        <div class="mb-rating">
                            <svg class="mb-start-fill">
                                <use href="#icon-star-active"></use>
                            </svg>
                            <svg class="mb-start-fill">
                                <use href="#icon-star-active"></use>
                            </svg>
                            <svg class="mb-start-fill">
                                <use href="#icon-star-active"></use>
                            </svg>
                            <svg class="mb-start-fill">
                                <use href="#icon-star-active"></use>
                            </svg>
                            <svg class="mb-start">
                                <use href="#icon-star-fill"></use>
                            </svg>
                        </div>
                        <div class="product-rating__selled">Đã bán 200+</div>
                    </div>
                </div>
                <p class="product-modal__desc">Đầm xòe dáng midi, tùng rã nhún, có đệm vai, không tay Đầm xòe dáng midi, tùng rã nhún, có đệm vai, không tay Đầm xòe dáng midi, tùng rã nhún, có đệm vai, không tay Đầm xòe dáng midi, tùng rã nhún, có đệm vai, không tay</p>
                <div class="product-modal__meta">
                    <div class="product-modal__meta-left">
                        <label class="product-modal__label-text">Số lượng</label>
                        <div class="product-modal__quantity-control quantity-control">
                            <div class="product-modal__quantity-inline">
                                <button class="product-modal__btn quantity__btn-down" type="button">
                                    <svg class="product-icon__down"><use href="#icon-down"></use></svg>
                                </button>
                                <input class="product-modal__quantity-input quantity-input quantity-input" type="text" value="1"/>
                                <button class="product-modal__btn quantity__btn-up" type="button">
                                    <svg class="product-icon__up"><use href="#icon-up"></use></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="product-modal__meta-right">
                        <label class="product-modal__label-text">Giá</label>
                        <div class="product-modal__price">
                        419.000đ <del class="product-modal__old-price">599.000đ</del>
                        </div>
                    </div>
                </div>
                <div class="product-modal__options product__info">
                    <div class="product-modal__options-left">
                        <label class="product-modal__label-text">Kích thước</label>
                        <ul class="product-size">
                            <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}]'
                            class="product-size__item maybi-hover">S
                            </li>
                            <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}]'
                            class="product-size__item maybi-hover">M
                            </li>
                            <li data-color='[{"color": "black", "img": "/static/images/blazer-den.jpg"}, {"color": "#607c2c", "img": "/static/images/blazer-xanh-reu.jpg"}, {"color": "#225c48", "img": "/static/images/blazer-green.jpg"}, {"color": "#c9e6ff", "img": "/static/images/blazer-blue.jpg"}, {"color": "#f32250", "img": "/static/images/blazer-pink.jpg"}, {"color": "#eb6609", "img": "/static/images/blazer-cam.jpg"}]'
                                class="product-size__item maybi-hover product-size--active">L
                                </li>
                        </ul>
                    </div>
                    <div class="product-modal__options-right">
                        <label class="product-modal__label-text">Màu</label>
                        <ul class="product-colors">
                            <li class="product-color active" data-id="pro4" data-img="http://localhost:7002/static/images/product1.png"><span class="product-color__item" style="background-color: black;"></span></li>
                            <li class="product-color" data-id="pro4" data-img="http://localhost:7002/static/images/product2.png"><span class="product-color__item" style="background-color: #607c2c;"></span></li>
                            <li class="product-color" data-id="pro4" data-img="http://localhost:7002/static/images/product3.png"><span class="product-color__item" style="background-color: #225c48;"></span></li>
                            <li class="product-color" data-id="pro4" data-img="http://localhost:7002/static/images/product3.png"><span class="product-color__item" style="background-color: #c9e6ff;"></span></li>
                            <li class="product-color" data-id="pro4" data-img="http://localhost:7002/static/images/product3.png"><span class="product-color__item" style="background-color: #f32250;"></span></li>
                            <li class="product-color" data-id="pro4" data-img="http://localhost:7002/static/images/product3.png"><span class="product-color__item" style="background-color: #eb6609;"></span></li>
                        </ul>
                    </div>
                </div>
                <div class="product-modal__actions">
                    <a href="#" class="btn btn--primary btn_add_to_cart">
                        <span>Thêm vào giỏ</span>
                    </a>
                    <a href={templates.AssetURL("/thanh-toan")} class="btn btn--outline">
                        <span>Mua ngay</span>
                    </a>
                    <div class="bookmark-btn style-1"><input class="form-check-input" id="favoriteCheck1" type="checkbox">
                        <label class="form-check-label" for="favoriteCheck1">
                            <svg class="product-icon__heart">
                                <use href="#icon-heart"></use>
                            </svg>
                        </label>
                    </div>
                </div>
            </div>

            <div class="product-modal__content-bot">
                <div class="product-modal__info">
                <p><strong>Mã sản phẩm:</strong> PRT584E63A</p>
                <p><strong>Danh mục:</strong> 
                    <a href="#" title="Đầm">Đầm</a>
                    <a href="#" title="Jeans">Jeans</a>
                    <a href="#" title="Công sở">Công sở</a>
                </p>
                <p><strong>Nhãn:</strong> 
                    <a href="#" title="Thường ngày">Thường ngày</a>
                    <a href="#" title="Thể thao">Thể thao</a>
                    <a href="#" title="Văn phòng">Văn phòng</a>
                </p>
                </div>
                <div class="product-modal__social-icon">
                    <ul>
                        <li>
                            <a target="_blank" class="text-dark" href="https://www.facebook.com/maybi.official" target="_blank">
                                <svg class="product-icon__social-item"><use href="#icon-facebook"></use></svg>
                            </a>
                        </li>
                        <li>
                            <a target="_blank" class="text-dark" href="https://www.instagram.com/maybi_official" target="_blank">
                                <svg class="product-icon__social-item"><use href="#icon-instagram"></use></svg>
                            </a>
                        </li>
                        <li>
                            <a target="_blank" class="text-dark" href="https://www.youtube.com/@thoitrangmaybi4893" target="_blank">
                                <svg class="product-icon__social-item"><use href="#icon-youtube"></use></svg>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
  </div>
</div>
}