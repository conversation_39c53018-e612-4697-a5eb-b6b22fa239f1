package checkout

import (
    "github.com/networld-solution/gos/templates"
    "goweb/views/layouts"
    "goweb/views/checkout/components"
    cartC "goweb/views/cart/components"
)

templ Checkout() {
    @layouts.Master(nil, &[]templ.Component{head()}, scriptSlot()) {
        <div class="checkout__wrapper">
            <div class="container90">
                <div class="checkout__box">
                    @components.CheckoutFormCpn()
                    @components.CheckoutSummaryCpn()
                </div>
            </div>
        </div>
        @cartC.OffcanvasVouchers()
    }
}
templ head(){
    <link rel="stylesheet" href={templates.AssetURL("/static/css/checkout.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/offcanvas_vouchers.css")}>
}

templ scriptSlot(){
    <script type="text/javascript" src={templates.AssetURL("/static/js/checkout.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/static/js/vouchers_process.js")}></script>
}