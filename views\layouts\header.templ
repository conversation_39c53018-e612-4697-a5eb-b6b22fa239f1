package layouts

import (
    "goweb/views/layouts/header"
    "github.com/networld-solution/gos/templates"
)

templ Header() {
<header class="header">
    @header.HeaderTop()
    <div class="header-wrapper">
        <div class="container90">
            <div class="header-navbars">
                @menuMobileBar()
                @logoMenuSticky()
                // <div class="group-icons-menus">
                //     <div class="m_extra_nav__item">
                //         <a href={templates.SafeURL("/tim-kiem")} title="Tìm kiếm" class="m_extra_nav__link">
                //             <svg class="m_extra_nav__icon" width="40" height="40">
                //                 <use xlink:href="#icon-search-line"></use>
                //             </svg>
                //         </a>
                //     </div>
                //     <div class="m_extra_nav__item">
                //         <a href={templates.SafeURL("/gio-hang")} title="Giỏ hàng" class="m_extra_nav__link">
                //             <svg class="m_extra_nav__icon" width="40" height="40">
                //                 <use xlink:href="#icon-shopping-cart"></use>
                //             </svg>
                //             <span class="m_extra_nav__count">4</span>
                //         </a>
                //     </div>
                // </div>
                @header.CategoryMenu()
                @header.NavMenu()
                @header.ActionIcons()
                @header.MenusMobile()
            </div>
        </div>
        @header.SearchDropdown()
    </div>    
</header>
}

templ logoMenuSticky() {
    <a class="maybi__logo" href={templates.AssetURL("/")} title="Maybi">                       
        <img class="maybi__logo__img" width="140" height="39" src={templates.AssetURL("/static/images/logo.webp")} alt="Maybi Ecommerce Logo"> 
    </a>
}

templ menuMobileBar() {
    <button class="menu-mobile-bar" type="button">
        <span></span>
        <span></span>
        <span></span>
    </button>
}