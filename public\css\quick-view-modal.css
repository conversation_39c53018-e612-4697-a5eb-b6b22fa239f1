.product-modal{
  position: fixed;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  font-size: var(--10px);
  overflow-y: auto;
}

.product-modal--hidden{
  display: none;
}

.product-modal__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--rgb-black), 0.6);
}

.product-modal__box{
  background: var(--white);
  position: relative;
  width: 65%;
  border-radius: 1.5em;
  z-index: 1;
}

.product-modal__close{
  position: absolute;
  top: .3em;
  right: 1em;
  font-size: 2em;
  border: none;
  background: none;
  cursor: pointer;
}

.product-modal__body{
  display: flex;
  align-items: flex-start;
  justify-items: center;
  height: 100%;
}

/* --- Image --- */
.product-modal__gallery{
  width: 50%;
  height: 100%;
}

.product-modal__main-img{
  width: 100%;
  border-radius: 0.5em;
  object-fit: cover;
}

.product-modal__thumbs{
  display: flex;
  gap: 0.5em;
  margin-top: 1em;
}

.product-modal__thumbs > img{
  width: 3.75em;
  height: 3.75em;
  object-fit: cover;
  cursor: pointer;
  border: .1em solid var(--light-grey);
}

/* --- Content --- */
.product-modal__content{
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2em 2.5em 0;
  overflow-y: auto;
}
.product-modal__content-top, .product-modal__content-bot{
  width: 100%;
  height: 100%;
}
.product-modal__content-box{
  display: flex;
  gap: 1em;
}

.product-modal__label{
  background: var(--black);
  color: var(--white);
  padding: .4em .8em;
  margin-bottom: 0.65em;
  font-size: 1em;
  border-radius: 1em;
  text-transform: uppercase;
  text-align: center;
  font-weight: 600;
}
.product-modal__label.product-modal__sale-percent{
  background-color: var(--primary);
}
.product-modal__label.product-modal__sale-hot{
  background-color: var(--light-dark);
}

.product-modal__title{
  margin: 1em 0;
  font-weight: 700;
}
.product-modal__title > a{
  font-size: 2.2em;
}

.product__quick-meta{
  display: flex;
  align-items: center;
  gap: 2em;
}

.bookmark-btn .form-check-input{
    display: none;
}
.bookmark-btn{
  display: flex;
  align-items: center;
  justify-content: center;
}
.bookmark-btn .form-check-label {
  height: 3.5em;
  width: 3.5em;
  cursor: pointer;
  text-align: center;
  display: block;
  background: var(--white);
  border-radius: 3em;
  color: var(--title);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 0.4em 0.6em rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.bookmark-btn .form-check-label::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.8;
  visibility: hidden;
  height: 100%;
  width: 100%;
  transform: translateX(-105%);
  border-right: 0.2em solid var(--white);
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.5s ease;
  pointer-events: none;
}
.bookmark-btn .form-check-label:hover::after {
  transform: translate(0);
  opacity: 0;
  visibility: visible;
}
.bookmark-btn .form-check-input:checked+.form-check-label{
    fill: var(--white);
    background-color: var(--primary);
    border: .1em solid var(--primary);
}
.bookmark-btn >label>svg{
  width: 1.8em;
  height: 1.8em;
}

.product-modal__desc{
  font-size: 1.3em;
  margin: 1.5em 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-modal__meta{
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-wrap: wrap;
}
.product-modal__meta-left{
  width: 40%;
}
.product-modal__meta-right{
  width: 60%;
}
.product-modal__meta-left, .product-modal__meta-right{
  height: 100%;
}
.product-modal__label-text{
  display: block;
  font-size: 1.3em;
  font-weight: 700;
  color: var(--black);
  background: transparent;
  margin-bottom: .5em;
}
.product-modal__options.product__info{
  padding: 0;
  box-shadow: none;
  margin: 2em 0;
}

.product-modal__price{
  font-size: 1.5em;
  font-weight: bold;
  margin-bottom: 0;
  color: var(--primary);
}

.product-modal__old-price{
  font-size: .75em;
  color: var(--light-grey);
  margin-left: 0.5em;
  text-decoration: line-through;
}

/* --- Quantity control --- */
.product-modal__quantity-control{
  display: flex;
  align-items: center;
}

.product-modal__quantity-inline{
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.product-modal__quantity-input{
  padding: 0.5em;
  font-size: var(--10px);
  text-align: center;
  border: .1em solid var(--black);
  width: 3em;
  height: 3em;
  border-radius: 50%;
}

.product-modal__btn{
  border: .1em solid var(--black);
  background-color: var(--black);
  color: var(--white);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-modal__btn >.product-icon__down, 
.product-modal__btn >.product-icon__up{
  font-size: var(--10px);
  width: 2.4em;
  height: 2.4em;
  fill: var(--white);
}
.product-modal__actions{
    padding: 0 0 2em;
    display: flex;
    gap: 1em;
    align-items: center;
}
/* --- Button --- */
.btn{
  padding: 0.75em 1.5em;
  cursor: pointer;
  border-radius: 0.7em;
  border: .1em solid var(--black);
}

.btn--primary > span, 
.btn--outline > span{
  font-size: 1.6em;
}

.btn--primary {
  position: relative;
  overflow: hidden;
  background: var(--black);
  display: flex;
  color: var(--white);
  z-index: 1;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.btn--primary:hover{
  color: var(--white);
}
.btn--primary::after,
.btn--outline::after{
  content: "";
  position: absolute;
  top:0;
  left: 0;
  opacity: 0.8;
  visibility: hidden;
  height: 100%;
  width: 100%;
  transform: translateX(-105%);
  border-right: 0.2em solid var(--white);
  background-color: rgba(255, 255, 255, .5);
  transition: all 0.5s ease;
  pointer-events: none;
}
.btn--primary:hover::after,
.btn--outline:hover::after{
  transform: translate(0);
  opacity: 0;
  visibility: visible;
}

.btn--outline {
  position: relative;
  overflow: hidden;
  background: var(--white);
  color: var(--black);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1em;
  white-space: nowrap;
  width: 100%;
}
.btn--outline:hover{
  background-color: var(--black);
  color: var(--white);
}
.btn--outline:hover >svg{
  fill: var(--white);
}

.product-modal__info{
  border-top: .1em solid rgba(var(--rgb-black), 0.2);
  display: flex;
  flex-direction: column;
  padding: 1.5em 0;
}
.product-modal__info >p{
  margin: 0.3em 0;
  font-size: 1.2em;
}
.product-modal__info >p>strong{
  font-size: 1.1em;
}
.product-modal__social-icon{
  width: 100%;
}
.product-modal__social-icon >ul{
  display: flex;
  align-items: center;
  gap: 1.5em;
}
.product-modal__social-icon li>a{
  display: flex;
  align-items: center;
}
.product-modal__social-icon .product-icon__social-item{
  width: 2em;
  height: 2em;
}
.product-modal__options{
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 2em;
}
.product-modal__options-left{
  position: relative;
}
.product-modal__options-left >.product-size{
  flex-direction: row;
  justify-content: flex-start;
  position: unset;
  width: 30%;
  opacity: 1;
}
.product-viewer{
  width: 50%;
  height: 100%;
  position: relative;
  aspect-ratio: 3 / 4;
}
.swiper-quick-view{
  width: 100%;
  height: 100%;
}
.product-viewer >.product-thumbnails{
  position: absolute;
  top: 2em;
  left: 2em;
  bottom: 2em;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 1em;
  max-height: calc(100% - 4em);
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.product-viewer >.product-thumbnails::-webkit-scrollbar {
  display: none;
}

/* Fade effect for scrollable thumbnails */
.product-viewer >.product-thumbnails::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1em;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-viewer >.product-thumbnails::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1em;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.1), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Show fade effects when content is scrollable */
.product-viewer >.product-thumbnails.scrollable::before,
.product-viewer >.product-thumbnails.scrollable::after {
  opacity: 1;
}
.product-viewer .product-thumbnails >img{
  border: .1em solid var(--white);
  object-fit: cover;
  width: 5em;
  height: 6em;
  cursor: pointer;
  border-radius: .36em;
  transition: 0.3s;
}
.product-viewer .product-thumbnails img.active{
  border-color: var(--primary);
}
.product__box.product__style,
.product__box .product__thumb-img{
  height: 100%;
}
.product__box .product__thumb-img >img{
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
  border-radius: 1.5em;
}

/* ---------- Responsive ---------- */
@media (max-width: 768px){
  .product-modal__box{
    width: 100%;
    max-height: 100vh;
    overflow-y: auto;
  }
  .product-modal__body{
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
  }
  .product-viewer{
    width: 100%;
  }
  .product-viewer >.product-thumbnails{
    flex-direction: row;
    top: unset;
    left: 4em;
    right: 2em;
    bottom: 1em;
    justify-content: flex-start;
    max-height: none;
    max-width: calc(100% - 6em);
    overflow-x: auto;
    overflow-y: hidden;
  }
  .product-modal__content{
    width: 100%;
    padding: 3em 2.5em 2em;
  }
  .product-viewer .product-thumbnails >img{
    object-fit: contain;
    width: 15em;
    height: 20em;
  }
  .product-modal__options-left >.product-size{
    width: 25%;
  }
  .product-modal__btn >.product-icon__down, .product-modal__btn >.product-icon__up{
    width: 3.6em;
    height: 3.6em;
  }
  .product-modal__quantity-input{
    width: 4em;
    height: 4em;
  }
  .product-modal__close {
    position: absolute;
    top: .2em;
    right: .5em;
    font-size: 8vw;
    border: none;
    background: none;
    cursor: pointer;
    z-index: 2;
  }
}
@media (max-width: 575px){
  .product-modal__content-top, .product-modal__content-bot {
    width: 100%;
    height: 100%;
    font-size: 3vw;
  }
  .product-modal__content .product-rating {
    font-size: 3vw;
  }
  .bookmark-btn .form-check-label {
    font-size: 2.5vw;
  }
  .product-modal__options.product__info {
    font-size: 3vw;
  } 
  .product-modal__options-left >.product-size {
    width: 40%;
  }
  .product-modal__options-right .product-colors {
    font-size: 0.825em;
  }
  .product-viewer .product-thumbnails >img {
    object-fit: contain;
    width: 10em;
    height: auto;
    aspect-ratio: 3 / 4;
  }
  .product-modal__quantity-input {
    width: 4em;
    height: 4em;
    font-size: 2.5vw;
  }
  .product-modal__btn >.product-icon__down, .product-modal__btn >.product-icon__up {
    width: 4.6em;
    height: 4.6em;
  }
  .btn--primary > span, .btn--outline > span {
    font-size: 1.4em;
  }
}